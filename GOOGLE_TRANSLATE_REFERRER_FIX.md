# Google Translate API Referrer Fix

## Problem
The Google Translate API was returning a 403 error with the message:
```
"Requests from referer <empty> are blocked."
"reason": "API_KEY_HTTP_REFERRER_BLOCKED"
```

This occurs when:
1. Your Google API key has HTTP referrer restrictions enabled in Google Cloud Console
2. Console commands (server-side) don't send referrer headers by default
3. Google's security policy blocks requests without proper referrer headers

## Solution Implemented

### 1. Enhanced GoogleTranslateService
**File:** `app/Model/Google/GoogleTranslateService.php`

- Added optional `$referrer` parameter to constructor
- Implemented HTTP client with custom referrer header injection
- Uses Guzzle middleware to add `Referer` header to all requests
- Maintains backward compatibility (works without referrer)

### 2. Updated Service Configuration
**File:** `app/config/services.neon`
```neon
googleTranslateService: App\Model\Google\GoogleTranslateService(%config.google.apiKey%, %config.google.referrer%)
```

### 3. Environment-Specific Referrer Configuration

**Base config** (`app/config/config.neon`):
```neon
google:
    apiKey: 'your-api-key'
    referrer: null  # Set to your domain if API key has referrer restrictions
```

**Environment configs:**
- **Development** (`app/config/environment.dev.neon`): `https://superadmin-dev.www6.superkoderi.cz/`
- **Stage** (`app/config/environment.stage.neon`): `https://sedlakovalegal-stage.www6.superkoderi.cz/`
- **Production** (`app/config/environment.prod.neon`): `https://sedlakovalegal-prod.www6.superkoderi.cz/`
- **Local Docker** (`app/config/config.local.neon`): `https://app.sedlakova-legal-2025.orb.local/`

### 4. Enhanced Commands
- **Original command:** `php bin/console google:translate` - now supports referrer
- **New improved command:** `php bin/console google:translate:improved` - with better error handling and testing

## Usage

### With Docker (Recommended)
```bash
# Test the translation service
docker compose run --rm -it app php bin/console google:translate:improved

# Use the original command
docker compose run --rm -it app php bin/console google:translate
```

### Direct PHP (if available)
```bash
php bin/console google:translate:improved
```

## Configuration Options

### Option 1: Use Referrer Header (Current Implementation)
Configure the referrer in your environment config:
```neon
google:
    referrer: "https://yourdomain.com/"
```

### Option 2: Create Server-Side API Key (Recommended for Production)
1. Go to [Google Cloud Console → APIs & Credentials](https://console.cloud.google.com/apis/credentials)
2. Create a new API key specifically for server-side use
3. Set **Application restrictions** to "None" or "IP addresses"
4. **Do NOT set HTTP referrer restrictions** for server-side keys
5. Update your config with the new API key

## Testing Results

✅ **Translation working:** "Jednoduchý text k překladu" → "Simple text to translate"
✅ **Batch translation working:** Multiple texts translated successfully
✅ **Error handling improved:** Clear guidance for common issues
✅ **Environment-specific configuration:** Different referrers for different environments

## Error Handling

The improved command provides detailed error messages and solutions:
- Invalid API key guidance
- Referrer configuration instructions
- Network connectivity troubleshooting
- Language code validation
- API quota information

## Security Considerations

1. **Server-side API keys** are more secure than client-side keys with referrer restrictions
2. **Referrer headers** can be spoofed, so they provide limited security
3. **IP restrictions** are more secure for server-to-server communication
4. **Environment variables** should be used for API keys in production

## Next Steps

1. **For production:** Consider creating a dedicated server-side API key without referrer restrictions
2. **For security:** Implement IP-based restrictions instead of referrer restrictions
3. **For monitoring:** Add logging for translation API usage and errors
