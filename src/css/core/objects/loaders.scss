@use 'base/variables';

%loader {
	top: 50%;
	left: 50%;
	width: 8rem;
	height: 8rem;
	border-radius: 50%;
	background: rgba(#000000, 0.5);
	opacity: 0;
	visibility: hidden;
	transition: opacity variables.$t 0s, visibility variables.$t 0s;
	transform: translate(-50%, -50%);
	&::after {
		content: '';
		position: absolute;
		top: 50%;
		left: 50%;
		width: 4rem;
		height: 4rem;
		margin: -2rem 0 0 -2rem;
		border: 0.4rem solid #ffffff;
		border-top-color: transparent;
		border-radius: 2rem;
	}
}

.block-loader {
	position: relative;
	transition: opacity variables.$t;
	&__loader {
		@extend %loader;
		position: absolute;
	}

	// STATES
	&.is-loading {
		opacity: 0.75;
		pointer-events: none;
	}
	&.is-loading &__loader {
		opacity: 1;
		visibility: visible;
		transition-delay: 0s, 0s;
		&::after {
			animation: animation-rotate 0.8s infinite linear;
		}
	}
}

.body-loader {
	&__loader {
		@extend %loader;
		position: fixed;
		z-index: 10000;
	}

	// STATES
	.is-loading &__loader {
		opacity: 1;
		visibility: visible;
		&::after {
			animation: animation-rotate 0.8s infinite linear;
		}
	}
}
