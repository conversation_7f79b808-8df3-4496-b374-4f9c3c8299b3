@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.component {
	position: relative;

	&::before {
		content: '';
		position: absolute;
		inset: 0;
		border: 0.1rem dashed transparent;
		border-radius: 10px;
		pointer-events: none;
		transition: border-color variables.$t;
	}
	&__title {
		position: absolute;
		top: -1.3rem;
		left: 1rem;
		z-index: 1;
		padding: 0 1rem;
		border-radius: 5px;
		background: variables.$color-white;
		visibility: hidden;
		transition: visbility variables.$t;
		box-shadow: 0 0 5px variables.$color-gray10;
	}

	&:hover::before {
		border-color: variables.$color-bd;
	}
	&:hover &__title {
		visibility: visible;
	}
	.demo &::before {
		border-color: variables.$color-bd;
	}
	.demo &__title {
		visibility: visible;
	}
}
