<?php declare(strict_types = 1);

namespace App;

use App\Components\VisualPaginator\VisualPaginatorFactory;
use App\Model\ConfigService;
use App\Model\DbalLog;
use App\Model\Image\ImageObjectFactory;
use App\Model\Orm\Orm;
use App\Model\Security\User;
use Nette\Application\UI\Presenter;
use Nette\DI\Attributes\Inject;
use Tracy\Debugger;

/**
 * @property-read User $user
 */
abstract class BasePresenter extends Presenter
{

	#[Inject]
	public ConfigService $configService;

	#[Inject]
	public ImageObjectFactory $imageObjectFactory;

	#[Inject]
	public Orm $orm;

	#[Inject]
	public VisualPaginatorFactory $visualPaginatorFactory;

	#[Inject]
	public DbalLog $dbaLog;

	protected ?Model\Orm\User\User $userEntity = null;

	protected function startup(): void
	{
		if ($this->user->id && $this->user->isLoggedIn()) {
			$userEntity = $this->orm->user->getById($this->user->id);
			if ($userEntity && $userEntity->id) {
				$this->userEntity = $userEntity;
			}
		} else {
			if ($this->user->id) {
				$this->user->logout(true);
			}
		}

		parent::startup();

		$this->dbaLog->register();
		if (!empty($this->configService->get('debuggerEditor'))) {
			Debugger::$editor = $this->configService->get('debuggerEditor');
		}
	}

	protected function beforeRender(): void
	{
		$this->template->webVersion = $this->configService->get('webVersion');
		$this->template->userEntity = $this->userEntity;

		$this->template->addFunction('dump', function (mixed $object, int $maxDepth = 2): void {
			$oldMaxDepth = Debugger::$maxDepth;
			Debugger::$maxDepth = $maxDepth;
			bdump($object);
			Debugger::$maxDepth = $oldMaxDepth;
		});
	}

	protected function afterRender()
	{
		parent::afterRender();

		// set maxDepth for router part of debug bar
		Debugger::$maxDepth = 2;
	}

}
