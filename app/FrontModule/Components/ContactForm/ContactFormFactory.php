<?php

declare(strict_types=1);

namespace App\FrontModule\Components\ContactForm;

use App\Model\Email\CommonFactory;
use App\Model\Form\CommonFormFactory;
use App\Model\Link\LinkFactory;
use App\Model\MakeWebhookClient;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\Routable;
use Nette\Application\UI\Form as UIForm;
use Nette\Forms\Form;
use Nette\Utils\ArrayHash;
use Throwable;
use Tracy\Debugger;

final readonly class ContactFormFactory
{
	public function __construct(
		private CommonFormFactory $formFactory,
		private MutationHolder $mutationHolder,
		private LinkFactory $linkFactory,
		private CommonFactory $commonEmailFactory,
		private MakeWebhookClient $makeClient,
	) {}

	public function create(
		Routable $object,
		bool $standalone = false,
	): Form
	{
		$form = $this->formFactory->create($standalone ? Form::class : UIForm::class);
		$form->addText('name', 'form_label_name')->setRequired();
		$form->addText('surname', 'form_label_surname')->setRequired();
		$form->addEmail('email', 'form_label_email')->setRequired();
		$form->addText('phone', 'form_label_phone')->setRequired();
		$form->addTextArea('text', 'form_label_text')->setRequired();
		// $form->addUpload('file', 'form_label_file');
		$form->addCheckbox('agree', 'form_label_agree')->setRequired();
		$form->addCheckbox('newsletter', 'form_label_newsletter');
		$form->addSubmit('send');

		$form->onSuccess[] = function (Form $form, ArrayHash $values) use ($object): void {
			$values->type = 'contact';
			$values->page = $object->getNameTitle();

			if (isset($object->template)) {
				if ($object->template == ':Feature:Front:Feature:servicesCategory' || $object->template == ':Feature:Front:Feature:servicesMain') {
					$values->type = 'serviceCategory';
				} else if ($object->template == ':Feature:Front:Feature:detail') {
					$values->type = 'service';
				} else if ($object->template == ':Front:Page:careerDetail') {
					$values->type = 'career';
				} else if ($object->template == ':Blog:Front:Blog:detail') {
					$values->type = 'blog';
				} else if ($object->template == ':Material:Front:Material:detail') {
					$values->type = 'material';
				}
			}

			$replyTo = [$values->email, $values->name];

			try {
				$values->pageLink = $this->linkFactory->linkTranslateToNette($object);
				$success = $this->makeClient->send((array)$values);
				$this->commonEmailFactory->create()
					->send(
						'',
						$this->mutationHolder->getMutation()->getRealAdminEmail(),
						'contact',
						(array)$values,
						null,
						$replyTo,
					);

			} catch (Throwable $error) {
				Debugger::log($error);
				bd($error->getMessage());
				$form->addError('Operation failed');
			}
		};

		return $form;
	}
}
