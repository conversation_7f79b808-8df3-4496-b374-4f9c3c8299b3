{varType App\Model\Orm\Order\Order $order}

<p class="message message--error" n:if="$order === null">{_order_not_found}</p>

{if $order !== null}

	<div class="b-order-detail">
		{foreach $flashes as $flash}
			<div class="message message-{$flash->type}">{_$flash->message}</div>
		{/foreach}

		{* Info *}
		<div class="u-mb-sm">
			<div class="grid grid--md">
				<div class="grid__cell size--6-12 u-mb-last-0">
					<table class="b-order-detail__table">
						<tbody>
							<tr>
								<th width="1px">{_'order_status'}:</th>
								<td>{_'order_status_' . $order->state->value}</td>
							</tr>
							<tr n:if="($paymentState = $order->payment->information->getState()) !== null">
								<th>{_'order_status_payment'}:</th>
								<td>{_'order_payment_status_user_' . $paymentState->value|lower}</td>
							</tr>
							<tr>
								<th>{_'order_created'}:</th>
								<td>{$order->placedAt|date:'j. n. Y, H:i'}</td>
							</tr>
							<tr>
								<th>{_'order_date_expedition'}:</th>
								<td>{if $order->delivery->dateExpedition !== null}{$order->delivery->dateExpedition|date:'j. n. Y'}{/if}</td>
							</tr>
							<tr>
								<th>{_'order_delivery'}:</th>
								<td>{$order->delivery->getName()}</td>
							</tr>
							<tr>
								<th>{_'order_payment'}:</th>
								<td>{$order->payment->getName()}</td>
							</tr>
							<tr>
								<th>{_'order_amount'}:</th>
								<td>{$order->products->count()}</td>
							</tr>
							<tr>
								<th>{_'order_total_price_vat'|noescape}:</th>
								<td>
									<strong>{$order->getTotalPriceWithDeliveryVat()|money} {_'price_tax'}</strong>
								</td>
							</tr>
						</tbody>
					</table>
				</div>
				<div n:if="$order->note" class="grid__cell size--6-12 u-mb-last-0">
					<p class="u-mb-0">
						{_'order_note'}
					</p>
					<p>
						{$order->note}
					</p>
				</div>
			</div>
		</div>

		<p class="u-mb-md">
			<button class="btn" type="button" onclick="window.print();return false;">
				<span class="btn__text">
					{_'order_print'}
				</span>
			</button>

			{* Storno *}
			{if !$order->isPaid() && !$order->isCanceled() && !$order->isDeclined()}
				<a n:href="cancel!, hash: $order->hash" data-naja data-naja-loader="body" data-naja-history="off" class="btn"> {* TODO: confirm *}
					<span class="btn__text">
						{_'cancel_order'}
					</span>
				</a>
			{/if}
		</p>

		{* Adresy *}
		<div class="u-mb-md">
			<div class="grid">
				<div class="grid__cell size--6-12">
					<h2 class="h4">{_'order_billing_address'}</h2>
					<p>
						{$order->name}<br>
						{if $order->companyName}{$order->companyName}<br>{/if}
						{$order->street}<br>
						{$order->city}<br>
						{$order->zip}
						{if $order->companyIdentifier}<br>{_'order_ic'}: {$order->companyIdentifier}<br>{/if}
						{if $order->vatNumber}{_'order_dic'}: {$order->vatNumber}{/if}
					</p>

					<p class="u-mb-0">
						<span class="item-icon">
							{('test-envelope')|icon, 'item-icon__icon'}
							<span class="item-icon__text">
								{$order->email}
							</span>
						</span>
					</p>
					<p class="u-mb-0">
						<span class="item-icon">
							{('test-phone')|icon, 'item-icon__icon'}
							<span class="item-icon__text">
								{$order->phone}
							</span>
						</span>
					</p>
				</div>

				<div class="grid__cell size--6-12">
					<h2 class=" h4">{_'order_delivery_address'}</h2>

					{if empty($order->dCity)}
						<p>
							{_'order_address_same_as_billing'}
						</p>
					{else}
						<p>
							{$order->dFirstname} {$order->dLastname}<br>
							{if $order->dCompany}{$order->dCompany}<br>{/if}
							{$order->dStreet}<br>
							{$order->dCity}<br>
							{$order->dZip}
						</p>
						<p n:if="$order->dPhone">
							<span class="item-icon">
								{('test-phone')|icon, 'item-icon__icon'}
								<span class="item-icon__text">
									{$order->dPhone}
								</span>
							</span>
						</p>
					{/if}
				</div>
			</div>
		</div>
	</div>

	{* Produkty *}
	<div class="f-basket u-mb-md" n:if="$order->products->count()">
		{define productItem}
			<tr class="f-basket__item">
				{varType App\Model\Orm\Order\Product\ProductItem $productItem}
				{php $link = false}
				{if $productItem->variant}
					{capture $link}{plink $productItem->variant->product, v: $productItem->variant->id}{/capture}
				{/if}
				<td class="f-basket__img">
					<a n:tag-if="$link" href="{$link}" class="img">
						{if $productItem->variant && $productItem->variant->product->firstImage}
							<img src="{$productItem->variant->product->firstImage->getSize('sm')->src}" alt="" loading="lazy">
						{else}
							<img src="/static/img/illust/noimg.svg" alt="" loading="lazy"/>
						{/if}
					</a>
				</td>
				<td class="f-basket__name">
					<a n:tag-if="$link" href="{$link}">
						{$productItem->getName()}
					</a>
				</td>
				<td class="f-basket__amount">
					{$productItem->amount}
				</td>
				<td class="f-basket__price">
					{$productItem->unitPriceVat|money}
				</td>
				<td class="f-basket__price">
					{$productItem->totalPriceVat|money}
				</td>
			</tr>
		{/define}

		{define voucherItem}
			<tr class="f-basket__item">
				{varType App\Model\Orm\Order\Voucher\VoucherItem $voucherItem}
				<td class="f-basket__img"></td>
				<td class="f-basket__name" colspan="3">
					{$voucherItem->getName()}
				</td>
				<td class="f-basket__price">
					{$voucherItem->unitPriceVat|money}
				</td>
				<td class="f-basket__price">
					{$voucherItem->totalPriceVat|money}
				</td>
			</tr>
		{/define}

		<table class="f-basket__table">
			<thead>
				<tr>
					<th colspan="2">
						{_'item_name'}
					</th>
					<th>
						{_'availability'}
					</th>
					<th>
						{_'unit_price_vat'}
					</th>
					<th>
						{_'total_price_vat'}
					</th>
				</tr>
			</thead>

			<tbody>
				{foreach $order->products as $productItem}{include productItem}{/foreach}
				{foreach $order->vouchers as $voucherItem}{include voucherItem}{/foreach}

				{* Doprava *}
				<tr class="f-basket__item">
					<td class="f-basket__name" colspan="4">
						{_'order_delivery'}: {$order->delivery->getName()}
					</td>
					<td class="f-basket__price">
						{if $order->delivery->unitPriceVat->isGreaterThan(0)}
							{$order->delivery->unitPriceVat|money}
						{else}
							{_'free'}
						{/if}
					</td>
				</tr>

				{* Platba *}
				<tr class="f-basket__item">
					<td class="f-basket__name" colspan="4">
						{_'order_payment'}: {$order->payment->getName()}
					</td>
					<td class="f-basket__price">
						{if $order->payment->unitPriceVat->isGreaterThan(0)}
							{$order->payment->unitPriceVat|money}
						{else}
							{_'free'}
						{/if}
					</td>
				</tr>
			</tbody>
		</table>

		<p class="u-ta-r u-mb-0">
			{_"total_price_vat"}: {$order->getTotalPriceWithDeliveryVat()|money}<br>
			{_"total_price"}: {$order->getTotalPriceWithDelivery()|money}
		</p>
	</div>

	<h2>
		{_'order_history_title'}
	</h2>
	<table>
		<tr n:foreach="$order->stateChanges as $history">
			<td>
				{$history->changedAt|date:'d. m. Y H:i:s'}
			</td>
			<td>
				{if $history->from !== null}{_'order_status_'.$history->from->value}{/if}
			</td>
			<td>
				{_'order_status_'.$history->to->value}
			</td>
		</tr>
	</table>
{/if}
