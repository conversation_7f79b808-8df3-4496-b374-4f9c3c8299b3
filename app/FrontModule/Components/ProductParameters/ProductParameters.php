<?php

declare(strict_types=1);

namespace App\FrontModule\Components\ProductParameters;

use App\Model\BucketFilter\CatalogParameter;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Product\Product;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;

/**
 * @property-read DefaultTemplate $template
 */
class ProductParameters extends UI\Control
{

	public function __construct(
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		private readonly Product $product,
		private readonly CatalogParameter $catalogParameter,
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);

		if ($mainCategory = $this->product->mainCategory) {
			$mainPageParameters = $this->catalogParameter->getParametersCfForFilter($mainCategory);
			$this->template->mainPageParametes = $mainPageParameters;
		}

		$productParameters = $this->orm->parameter->findParametersForProduct($this->product);

		$filtrableUids = [];
		if (isset($mainPageParameters->visibleParameters)) {
			foreach ($mainPageParameters->visibleParameters as $visibleParameter) {
				if ($parameter = $visibleParameter->parameter) {
					if ($parameter->type == Parameter::TYPE_NUMBER) {
						if ( ! isset($visibleParameter->numberAsRange) || ! $visibleParameter->numberAsRange) {
							$filtrableUids[] = $parameter->uid;
						}
					} else {
						$filtrableUids[] = $parameter->uid;
					}
				}
			}
		}


		$this->template->product = $this->product;
		$this->template->mainCategory = $this->product->mainCategory;
		$this->template->filtrableUids = $filtrableUids;

		$this->template->productParameters = $productParameters;
		$this->template->render(__DIR__ . '/productParameters.latte');
	}

}
