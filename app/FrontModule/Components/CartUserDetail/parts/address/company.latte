{varType App\Model\Mutation $mutation}
{default $nameSuffix = ''}
{default $class = false}

<div n:class="f-open, $class" data-controller="toggle-class">
	<p>
		<label class="inp-item inp-item--checkbox">
			{var $inputName = "companyTab$nameSuffix"}
			<input n:name=$inputName value="1" class="inp-item__inp" data-action="change->toggle-class#toggleInp">
			<span class="inp-item__text">
				{_'title_company_form'}
			</span>
		</label>
	</p>
	<div n:class="f-open__box, u-mb-last-0, $form['companyTab'.$nameSuffix]->value ? is-open">
		{include '../../../../Components/inp.latte', form=>$form, name=>'inv_company'.$nameSuffix, required=>true, validate: true}
		{include '../../../../Components/inp.latte', form=>$form, name=>'inv_ic'.$nameSuffix, required=>true, validate: true}
		{include '../../../../Components/inp.latte', form=>$form, name=>'inv_dic'.$nameSuffix, validate: true}
	</div>
</div>

