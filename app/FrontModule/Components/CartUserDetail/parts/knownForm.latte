<form n:name="signInForm" class="f-basket" novalidate="novalidate" data-naja data-naja-loader="body" data-naja-history="off">
	<h2 class="u-mb-sm  h3">
		1. {_'personal_info_title'}
	</h2>
	{control formMessage, $flashes, $form}
	<a n:href="changeEmail!" data-naja data-naja-history="off" data-naja-loader="body">{_order_change_email}</a>
	{include '../../inp.latte', form: $form, name: email, validate: true}
	{include '../../inp.latte', form: $form, name: password, required: true, validate: true}

	<p>
		{capture $link}{plink $pages->userChangePassword}{/capture}
		{_'note_step2_forgot_password'|replace:'%link', $link->__toString()|noescape}
	</p>

	<div class="u-maw-3-12 u-mb-last-0">
		<p>
			<button n:name="save" class="btn btn--block">
				<span class="btn__text">
					{_'btn_login'}
				</span>
			</button>
		</p>
		<p>
			<button n:name="next" class="btn btn--secondary btn--block">
				<span class="btn__text">
					{_'btn_step2_continue_without_signin'}
				</span>
			</button>
		</p>
	</div>
</form>
