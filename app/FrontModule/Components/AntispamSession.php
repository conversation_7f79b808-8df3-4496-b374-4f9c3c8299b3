<?php

namespace App\FrontModule\Components;

use Nette\Http\IRequest;
use Nette\Http\Session;
use Nette\Http\SessionSection;
use Nette\Utils\Random;

class AntispamSession
{

	public const SESSION_NAME = 'antispam';

	private SessionSection $sessionSection;

	public function __construct(
		Session $session,
		private readonly IRequest $httpRequest,
	)
	{
		$this->sessionSection = $session->getSection(self::SESSION_NAME);
	}


	public function prepareAntispam(): void
	{
		if (!isset($this->sessionSection->antispam) || !$this->sessionSection->antispam) {
			$this->sessionSection->antispam = [
				'hash' => Random::generate(8),
				'ip' => $this->httpRequest->getRemoteAddress(),
				'noJsValue' => rand(1, 999),
			];
		}
	}

}
