{default $query = ""}
{default $pager = true}
{block content}
	<div class="row-main row-main--content u-maw-sm u-mb-md">
		{include $templates.'/part/form/search.latte'}

		<h1 class="h3 u-mt-xs u-ta-c u-mb-sm u-vhide">
			{$object->name}{if isset($search)}: {$search} {/if}
		</h1>

		<div n:snippet="tabContent" data-ajax-append>
			{if $hasResults}
				{include './parts/tabs/pages.latte'}
			{else}
				{* {include './parts/noresults.latte'} *}
			{/if}
		</div>
		{if $hasResults}
			{control pager, [class: 'u-pt-sm u-pt-md@md', showPages: true, showMoreBtn: true]}
		{/if}

		{snippet catalogFooter}
			{include $templates . '/part/box/content.latte'}
		{/snippet}
	</div>
{/block}

