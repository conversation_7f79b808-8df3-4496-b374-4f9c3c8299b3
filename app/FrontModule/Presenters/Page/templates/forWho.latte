{block content}
	{snippet content}
		{include $templates.'/part/box/annot.latte'}
		{control customContentRenderer}
		{include $templates.'/part/box/contact.latte', class: 'u-pt-xl u-bgc-white', title: 'forwho_contact_title'}
		{default $cf = $object->cf?->blogs ?? false}
		{if $cf}
			{include $templates.'/part/crossroad/casestudies.latte', items: $cf->items, class: 'u-mb-0 u-pb-md u-pb-lg@md u-pt-md u-pt-lg@md u-bgc-yellow', classGrid: 'grid grid--center', classItem: 'grid__cell grid__cell--eq size--6-12@sm size--4-12@lg', title: $cf->title}
		{/if}
	{/snippet}
{/block}

{* {include $templates.'/part/box/content.latte'} *}
