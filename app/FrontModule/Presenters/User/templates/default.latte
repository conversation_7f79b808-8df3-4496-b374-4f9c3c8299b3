{block content}
	<div class="row-main">
		<div class="u-pt-sm u-mb-last-0 u-mb-xl">
			{control breadcrumb}

			<div class="grid">
				<div class="grid__cell size--3-12">
					{control userSideMenu}
				</div>
				<div class="grid__cell size--9-12">
					{if isset($order->orderNumber)}
						{capture $title}{_'order_detail_title'} {$order->orderNumber}{/capture}
						{include $templates . '/part/box/annot.latte', name=>$title->__toString()}
					{else}
						{include $templates . '/part/box/annot.latte'}
					{/if}

					{include $templates . '/part/box/content.latte'}

					{if $object->uid == 'userProfil'}
						{control googleConnect}
						{control profileForm}
					{elseif $object->uid == 'userChangePassword'}
						{control changePasswordForm}
					{elseif $object->uid == 'userAddress'}
						{control userAddressForm}
					{elseif $object->uid == 'userOrderHistory'}
						{ifset $order}
							{snippet orderHistoryDetail}
								{control orderHistory:detail, $presenter->getParameter('orderHash')}
							{/snippet}
						{else}
							{snippet orderHistoryControl}
								{control orderHistory}
							{/snippet}
						{/ifset}
					{/if}
				</div>
			</div>
		</div>
	</div>

