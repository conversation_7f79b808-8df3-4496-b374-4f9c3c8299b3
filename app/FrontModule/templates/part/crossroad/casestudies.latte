{default $class = 'u-bgc-secondary u-pt-lg u-pb-lg u-mb-md u-mb-lg@md'}
{default $id = 'casestudies_cf'}
{default $classGrid = 'grid grid--center'}
{default $classItem = 'grid__cell grid__cell--eq size--6-12@sm size--3-12@lg'}
{default $cf = $object->cf?->casestudies ?? false}
{default $title = $cf->title ?? false}
{default $annot = $cf->annot ?? false}
{default $items = $cf->items ?? []}
{default $link = $cf->link ?? false}

{embed $templates.'/part/core/component.latte', id: $id, user: $user}
	{block content}
		<section n:attr="id: $id" n:if="count($items)" n:class="c-casestudies, $class">
			<div class="row-main">
				<div class="u-mb-last-0 u-mb-md">
					<h2 n:if="$title" class="h4 c-casestudies__title u-ta-c u-mb-sm">{$title}</h2>
					<p n:if="$annot" class="c-casestudies__annot u-ta-c">{$annot}</p>
				</div>
				<div class="u-mb-sm u-mb-md@md">
					<div n:class="$classGrid">
						<div n:if="isset($item) && $item" n:foreach="$items as $item" n:class="$classItem">
							{include $templates.'/part/box/casestudy.latte', class: false, item: $item}
						</div>
					</div>
				</div>
				<p n:if="$link" class="u-ta-c u-mb-0 u-mt-md">
					{include $templates . '/part/core/linkChoose.latte', item: $link, class: "c-casestudies__btn btn btn--bd", isButton: true, hasArrow: true}
				</p>
			</div>
		</section>
	{/block}
{/embed}
