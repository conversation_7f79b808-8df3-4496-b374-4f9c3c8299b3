{* {dump $object->template} *}
{* {dump $object->uid} *}

{* Style split styles *}
{* <link rel="stylesheet" href="/static/css/common.css?v={$webVersion}">
{if isset($object->uid) && $object->uid == 'title'} *}
	{* Landing *}
	{* <link rel="stylesheet" href="/static/css/landing.css?v={$webVersion}">
{elseif str_contains($object->template, ':Front:Catalog:') || in_array($object->template, [':Tag:Front:Tag:detail', ':Front:Search:default', ':Writer:Front:Writer:detail', ':Publisher:Front:Publisher:detail', ':Theme:Front:Theme:detail', ':Front:Product:detail', ':Discount:Front:Discount:detail'])} *}
	{* Eshop / Catalog *}
	{* <link rel="stylesheet" href="/static/css/eshop.css?v={$webVersion}">
{elseif str_contains($object->template, ':Front:User:')} *}
	{* User *}
	{* <link rel="stylesheet" href="/static/css/user.css?v={$webVersion}">
{elseif in_array($object->template, [':Front:Order:default', ':Front:Order:step1', ':Front:Order:step2', ':Front:Order:step3'])} *}
	{* Order *}
	{* <link rel="stylesheet" href="/static/css/order.css?v={$webVersion}">
	<link rel="stylesheet" href="/static/css/delivery-picker.css?v={$webVersion}">
{else} *}
	{* Other *}
	{* <link rel="stylesheet" href="/static/css/other.css?v={$webVersion}">
{/if}
<link rel="stylesheet" href="/static/css/layout-utilities.css?v={$webVersion}"> *}

{* Non style split styles *}
<link rel="stylesheet" href="/static/css/style.css?v={$webVersion}">

<link rel="stylesheet" href="/static/css/print.css?v={$webVersion}" media="print">
<link n:if="isset($user) && $user->isDeveloper()" rel="stylesheet" href="/static/css/admin.css?v={$webVersion}">
