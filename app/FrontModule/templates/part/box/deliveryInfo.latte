{varType App\Model\DeliveryDate $deliveryDate}
{default $deliveryDate = false}
{default $spanOnRequest = false}


{if $deliveryDate}
	{*if $transportType != 'personal' && $mutation->isInternational && !$isDetail}
		{_'detail_estimated_delivery'|lower}:
	{/if*}
	{if $deliveryDate->isInterval()}
		{if $deliveryDate->isIntervalSameMonth()}
			{if $mutation->isInternational}
				{$deliveryDate->from|date:'F j'} - {$deliveryDate->to|date:'j.'}
			{else}
				{$deliveryDate->from|date:'j.'} - {$deliveryDate->to|date:'j. n.'}
			{/if}
		{else}
			{if $mutation->isInternational}
				{$deliveryDate->from|date:'F j'} - {$deliveryDate->to|date:'F j.'}
			{else}
				{$deliveryDate->from|date:'j. n.'} - {$deliveryDate->to|date:'j. n.'}
			{/if}
		{/if}
	{elseif $deliveryDate->isToday()}
		{_'delivery_date_today'} {$deliveryDate->from|date:'j. n.'}
	{elseif $deliveryDate->isTomorrow()}
		{_'delivery_date_tomorrow'} {$deliveryDate->from|date:'j. n.'}
	{else}
		{var $dayOfTheWeek = $deliveryDate->getDayOfTheWeek()}

		{*if $mutation->isInternational}
			{_}day_{$dayOfTheWeek}{/_} {$deliveryDate->from|date:'F j.'}
		{else*}
			{if $dayOfTheWeek == 3 || $dayOfTheWeek == 4}{_'delivery_date_at_inf'}{else}{_'delivery_date_at'}{/if}
			{translate}day_{$dayOfTheWeek}{/translate} {$deliveryDate->from|date:'j. n.'}
		{*/if*}
	{/if}
	{*if $transportType != 'personal' && !$mutation->isInternational}
		{_'delivery_date_at_yours'}
	{/if*}

{elseif is_null($deliveryDate)}
	<span n:tag-if="$spanOnRequest" class="availability availability--unavailable">
		{_'availability_on_request'}
	</span>
{/if}
