{default $class = 'u-bgc-white'}
{default $id = 'contact_person_cf'}
{default $hideTitle = false}

{embed $templates.'/part/core/component.latte', id: $id, user: $user}
	{block content}
		<div n:attr="id: $id" n:class="$class, 'u-pt-md u-pt-lg@md u-pb-md u-pb-lg@md'">
			<div class="row-main">
				<div n:class="b-contact-person, u-maw-10-12, u-mx-auto, u-mb-xs">
					<h2 n:if="!$hideTitle" class="h4 u-ta-c u-mb-md">
						{_"title_contact_person_form"}
					</h2>
					<div class="b-contact-person__box">
						<div class="b-contact-person__grid grid grid--x-0 grid--y-0">
							<div class="b-contact-person__left u-bgc-white grid__cell">
								{control contactForm, itemClass=>'size--12-12'}
							</div>

							{if isset($featureLocalization->categories)}
								{foreach $featureLocalization->categories as $category}
									{default $cf = $category->cf ?? false}
								{/foreach}
							{elseif isset($object->cf)}
								{default $cf = $object->cf ?? false}
							{/if}

							{if isset($object->authors) && $object->authors}
								{foreach $object->authors as $author}
									{default $person = $author ?? false}
								{/foreach}
								<div n:if="isset($person)" class="b-contact-person__right grid__cell link-mask">
									{default $link = $person ?? false}
									<div class="b-contact-person__person">
										<div class="u-maw-3-12 u-mx-auto u-mb-last-0">
											<p class="u-fw-b u-mb-sm">
												{_"contact_person_form_person"}
											</p>
											<p class="u-mb-sm">
												<a n:href="$link" class="b-author__link link-mask__link aaa">
													{default $img = isset($person->author->cf->base->mainImage) ? $person->author->cf->base->mainImage->getEntity() ?? false : false}
													{if $img}
														<img class="img img--3-4" src="{$img->getSize('md-3-4')->src}" alt="{$img->getAlt($mutation)}" loading="lazy">
													{else}
														<img class="img img--3-4" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
													{/if}
												</a>
											</p>
											<p class="u-fw-b u-mb-sm">
												{$person->name}
											</p>
											<p n:if="$person->authorTags" class="b-contact-person__tags">
												{foreach $person->authorTags as $i => $tag}
													<span class="btn btn--square{if $i > 0} btn--gray{/if}">
														<span class="btn__text">{$tag->name}</span>
													</span>
												{/foreach}
											</p>
										</div>
									</div>
								</div>
							{elseif isset($cf) && $cf}
								{default $person = isset($cf->contactPerson->author) ? $cf->contactPerson->author->getEntity() ?? false : false}

								<div n:if="$person" class="b-contact-person__right grid__cell link-mask">
									{default $link = $person ?? false}
									<div class="b-contact-person__person">
										<div class="u-maw-3-12 u-mx-auto u-mb-last-0">
											<p class="u-fw-b u-mb-sm">
												{_"contact_person_form_person"}
											</p>
											<p class="u-mb-sm">
												<a n:href="$link" class="b-author__link link-mask__link bbb">
													{default $img = isset($person->author->cf->base->mainImage) ? $person->author->cf->base->mainImage->getEntity() ?? false : false}
													{if $img}
														<img class="img img--3-4" src="{$img->getSize('md-3-4')->src}" alt="{$img->getAlt($mutation)}" loading="lazy">
													{else}
														<img class="img img--3-4" src="/static/img/illust/noimg.svg" alt="" loading="lazy">
													{/if}
												</a>
											</p>
											<p class="u-fw-b u-mb-sm">
												{$person->name}
											</p>
											<p n:if="$person->author->tags" class="b-contact-person__tags">
												{foreach $person->authorTags as $i => $tagLocalization}
													<span class="btn btn--square{if $i > 0} btn--gray{/if}">
														<span class="btn__text">{$tagLocalization->name}</span>
													</span>
												{/foreach}
											</p>
										</div>
									</div>
								</div>
							{/if}
						</div>
					</div>
				</div>
			</div>
		</div>
	{/block}
{/embed}

