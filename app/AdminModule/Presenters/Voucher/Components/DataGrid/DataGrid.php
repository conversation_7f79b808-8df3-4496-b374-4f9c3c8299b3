<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Voucher\Components\DataGrid;


use App\Model\Orm\Voucher\Voucher;
use App\Model\Orm\Voucher\VoucherRepository;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\DbalCollection;
use Ublaboo\DataGrid\Exception\DataGridException;

/**
 * @property-read DefaultTemplate $template
 */
class DataGrid extends Control
{

	public function __construct(
		private readonly Translator $translator,
		private readonly VoucherRepository $voucherRepository,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$grid->setDataSource($this->voucherRepository->findAll());
		$grid->addColumnText('internalName', 'internalName')->setSortable()->setFilterText();
		$grid->addColumnText('name', 'name')->setSortable()->setFilterText();
		$grid->addColumnText('public', 'public')->setReplacement([
			1 => $this->translator->translate('public'),
			0 => $this->translator->translate('non-public'),
		]);

		$grid->addColumnText('isActive', 'isActive')->setReplacement([
			1 => $this->translator->translate('isActive'),
			0 => $this->translator->translate('nonActive'),
		]);

		$grid->addColumnText('type', 'type')->setReplacement([
			Voucher::TYPE_PERCENT => $this->translator->translate('voucher_' . Voucher::TYPE_PERCENT),
			Voucher::TYPE_AMOUNT => $this->translator->translate('voucher_' . Voucher::TYPE_AMOUNT),
			Voucher::TYPE_AMOUNT_COMBINATION => $this->translator->translate('voucher_' . Voucher::TYPE_AMOUNT_COMBINATION),
		]);
		/*$grid->addColumnText('kind', 'kind')->setReplacement([
			Voucher::KIND_NORMAL => $this->translator->translate('voucher_kind_' . Voucher::KIND_NORMAL),
			Voucher::KIND_PRODUCT => $this->translator->translate('voucher_kind_' . Voucher::KIND_PRODUCT),
		]);*/

		$grid->addColumnDateTime('publicTime', 'publicTime')
		     ->setRenderer(function (Voucher $voucher): string {
			     $ret = [];
			     if ($voucher->publicFrom !== null) {
				     $ret[] = 'od ' . $voucher->publicFrom->format('Y-m-d H:i:s');
			     }

			     if ($voucher->publicTo !== null) {
				     $ret[] = 'do ' . $voucher->publicTo->format('Y-m-d H:i:s');
			     }

			     return implode(' ', $ret);
		     })
		     ->setFilterDateRange()
		     ->setCondition(function (DbalCollection $collection, ArrayHash $values): void {
			     $from = null;
			     $to = null;
			     $now = new DateTimeImmutable();
			     if (isset($values->from) && $values->from !== '') {
				     $from = DateTimeImmutable::createFromFormat('j. n. Y', $values->from);
				     if ($from !== false) {
					     $from = $from->setTime(0, 0, 0);
				     } else {
					     $from = null;
				     }
			     }

			     if (isset($values->to) && $values->to !== '') {
				     $to = DateTimeImmutable::createFromFormat('j. n. Y', $values->to);
				     if ($to !== false) {
					     $to = $to->setTime(23, 59, 59);
				     } else {
					     $to = null;
				     }
			     }

			     if ($from !== null && $to !== null) {
				     $collection->getQueryBuilder()->andWhere(
					     'publicTo > %dt and publicFrom < %dt',
					     $from,
					     $to
				     );

			     } elseif ($from !== null) {
				     $collection->getQueryBuilder()->andWhere(
					     'publicTo > %dt',
					     $from
				     );

			     } elseif ($to !== null) {
				     $collection->getQueryBuilder()->andWhere(
					     'publicFrom < %dt',
					     $to,
					     $now
				     );
			     }
		     });

		$grid->addAction('edit', 'Edit', ':edit')->setClass('btn btn-xs btn-primary');

		$grid->setTranslator($this->translator);

		return $grid;
	}

}
