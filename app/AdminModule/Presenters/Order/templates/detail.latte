{layout $templates.'/@layout-new.latte'}
{varType App\Model\Orm\Order\Order $order}

{block #content}
<div class="main__main main__main--one-column">
	<div class="main__header">
		{include $templates.'/part/box/header.latte',
			props: [
				title: $translator->translate('Orders') . ': ' . $order->orderNumber,
				isPageTitle: true,
			]
		}
	</div>
	<div class="main__content scroll">
		{snippet flash}
			<div n:foreach="$flashes as $flash" class="message message-{$flash->type}">{$flash->message}</div>
		{/snippet}

		<ul>
			<li>
				<strong>E-mail:</strong>
				<br/>
				<a
					n:href="User:edit, id: $order->user->id ?? 0"
					n:tag-if="$order->user !== null"
				>
					{$order->email}
				</a>
			</li>
			<li>
				<strong>Telefon:</strong>
				<br/>
				{$order->phone}
			</li>
			<li>
				<strong>J<PERSON>no:</strong>
				<br/>
				{$order->name}
			</li>
			<li>
				<strong>Fakturační údaje:</strong>
				<br/>
				{if $order->companyName !== null}{$order->companyName}<br/>{/if}
				{$order->street}<br/>
				{$order->city}<br/>
				{$order->zip}<br/>
				{$order->country->name}<br/>
				{if $order->companyIdentifier !== null}ID# {$order->companyIdentifier}<br/>{/if}
				{if $order->vatNumber !== null}VAT ID {$order->vatNumber}{/if}
			</li>
		</ul>

		<h2>Položky objednávky</h2>
		<table>
			<thead>
				<tr>
					<th>Položka</th>
					<th>Množství</th>
					<th>Cena / jedn.</th>
					<th>Cena celkem</th>
					<th>Sazba DPH</th>
					<th>Cena s DPH</th>
				</tr>
			</thead>

			<tbody>
				<tr n:foreach="$order->products as $product">
					<td>
						<a n:href="Product:edit, id: $product->variant->product->id">
							{$product->variant->name}
						</a>
					</td>
					<td>{$product->amount}</td>
					<td>{$product->unitPrice->asMoney()|money}</td>
					<td>{$product->totalPrice|money}</td>
					<td>{$product->vatRate->value}</td>
					<td>{$product->totalPriceVat|money}</td>
				</tr>
				<tr n:foreach="$order->vouchers as $voucher">
					<td>{$voucher->getName()}</td>
					<td>{$voucher->amount}</td>
					<td>{$voucher->unitPrice->asMoney()|money}</td>
					<td>{$voucher->totalPrice|money}</td>
					<td>{$voucher->vatRate->value}</td>
					<td>{$voucher->totalPriceVat|money}</td>
				</tr>
				<tr>
					<td colspan="3"><strong>Doprava:</strong> {$order->delivery->deliveryMethod->name}</td>
					<td>{$order->delivery->totalPrice|money}</td>
					<td>{$order->delivery->vatRate->value}</td>
					<td>{$order->delivery->totalPriceVat|money}</td>
				</tr>
				<tr>
					<td colspan="3"><strong>Platba:</strong> {$order->payment->paymentMethod->name}</td>
					<td>{$order->payment->totalPrice|money}</td>
					<td>{$order->payment->vatRate->value}</td>
					<td>{$order->payment->totalPriceVat|money}</td>
				</tr>
			</tbody>

			<tfoot>
				<tr>
					<td colspan="5"></td>
					<th>
						{$order->getTotalPriceWithDeliveryVat()|money}
					</th>
				</tr>
			</tfoot>
		</table>

		<h2>Historie objednávky</h2>
		<table>
			<thead>
				<tr>
					<th>Datum a čas</th>
					<th>Původní stav</th>
					<th>Nový stav</th>
				</tr>
			</thead>
			<tbody>
				<tr n:foreach="$order->stateChanges as $stateChange">
					<td>{$stateChange->changedAt|date:'j.n.Y H:i'}</td>
					<td>{$stateChange->from?->value}</td>
					<td>{$stateChange->to->value}</td>
				</tr>
			</tbody>
		</table>
	</div>
</div>
