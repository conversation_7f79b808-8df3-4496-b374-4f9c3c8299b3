<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Cache\Components\DataGrid;

use App\Model\Orm\Alias\AliasModel;
use App\Model\Router\Filter;
use App\Model\Translator;
use JetBrains\PhpStorm\NoReturn;
use Nette\Application\UI\Control;
use Nette\Caching\Cache;
use Nette\Caching\Storage;
use Ublaboo\DataGrid\Column\Action\Confirmation\CallbackConfirmation;
use Ublaboo\DataGrid\Exception\DataGridException;

class DataGrid extends Control
{

	/** @var array<string, string> */
	private static array $data = [
		'aliases' => AliasModel::class,
		'routerFilters' => Filter::class,
	];

	public function __construct(
		private readonly Storage $cacheStorage,
		private readonly Translator $translator,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$grid->setPrimaryKey('namespace');
		$grid->setDataSource($this->prepareData(self::$data));

		$grid->addColumnText('namespace', 'Namespace', 'namespace')->setFilterText();

		$grid->setTranslator($this->translator);

		$grid->addAction('delete', '', 'delete!')
			->setTitle('Odstranit')
			->setIcon('trash')
			->setConfirmation(
				new CallbackConfirmation(
					function ($item) {
						return 'Opravdu si přejete vymazat tuto cache?';
					}
				)
			);

		return $grid;
	}


	public function handleDelete(string $namespace): never
	{
		if (array_key_exists($namespace, self::$data)) {
			$namespaceName = self::$data[$namespace];
			$cache = new Cache($this->cacheStorage, $namespaceName);
			$cache->clean([Cache::All => true]);
		}

		$this->presenter->flashMessage('success', 'ok');
		$this->presenter->redirect('this');
	}

	private function prepareData(array $data): array
	{
		$dataForDataGrid = [];
		foreach (array_keys($data) as $namespace) {
			$dataForDataGrid[] = ['namespace' => $namespace];
		}

		return $dataForDataGrid;
	}

}
