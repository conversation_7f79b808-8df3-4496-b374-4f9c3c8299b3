{layout $templates.'/@layout-new.latte'}
{block #content}
<div class="main__main">
	<div class="main__header">
		{include $templates.'/part/box/header.latte',
		props: [
		hrefClose: 'default',
		img: '',
		title: "Form data",
		hasGeneratedMenu: false,
		isPageTitle: true,
		]
		}
	</div>
	<div class="main__content scroll">
		<p>Vytvořeno <strong>{$object->createdAt|date:'d. m. Y H:i:s'}</strong></p>
		<table class="table table-hover table-striped table-bordered table-sm">
			<thead>
				<tr>
					<th><PERSON><PERSON><PERSON><PERSON></th>
					<th>Hodnota</th>
				</tr>
			</thead>
			<tbody>
				<tr n:foreach="$formData as $key => $value">
					<td><strong>{$key}</strong></td>
					<td>{\Tracy\Dumper::toHtml($value)|noescape}</td>
				</tr>

			</tbody>
		</table>
	</div>
	<div class="main__content-side scroll">
	</div>
</div>
