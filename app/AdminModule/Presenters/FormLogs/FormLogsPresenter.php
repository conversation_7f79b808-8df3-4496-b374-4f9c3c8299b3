<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\FormLogs;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\FormLogs\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\FormLogs\Components\DataGrid\DataGridFactory;

final class FormLogsPresenter extends BasePresenter
{
	public function __construct(

		private readonly DataGridFactory $dataGridFactory,
	)
	{
		parent::__construct();
	}

	public function renderDetail(int $id): void
	{
		$object = $this->orm->log->getById($id);

		$this->template->object = $object;
		$this->template->formData = array_filter(iterator_to_array($object->formData), function ($value, $key): bool {
			return !str_starts_with($key, 'antispam');
		}, ARRAY_FILTER_USE_BOTH);
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create();
	}

}
