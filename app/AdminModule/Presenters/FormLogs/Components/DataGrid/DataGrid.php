<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\FormLogs\Components\DataGrid;

use App\Model\Orm\Log\Log;
use App\Model\Orm\Log\LogRepository;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Translator;
use Nette\Application\Responses\FileResponse;
use Nette\Application\UI\Control;
use Nette\Utils\Json;
use Nextras\Orm\Collection\ICollection;
use Ublaboo\DataGrid\Exception\DataGridException;

class DataGrid extends Control
{

	public function __construct(
		private readonly string $fileLogDirectoryPath,
		private readonly LogRepository $logRepository,
		private readonly MutationRepository $mutationRepository,
		private readonly Translator $translator,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();


		$source = $this->logRepository->findForDataGrid()->orderBy('createdAt', ICollection::DESC);
		$grid->setDefaultPerPage(40);
		$grid->setDataSource($source);
		$grid->addColumnText('type', 'type')->setRenderer(
			function (Log $log): string {
				if ($log->message === 'careerForm-form') {
					return $this->translator->translate('Kariéra');
				} elseif ($log->message === 'configurationOrderForm-form') {
					return $this->translator->translate('Poptávka');
				} elseif ($log->message === 'onDemand-form') {
					return $this->translator->translate('Dotaz na dostupnost');
				}

				if (strpos($log->message, 'newsletterForm') !== false) {
					return $this->translator->translate('Odběr novinek');
				}
				if (strpos($log->message, 'serviceForm') !== false) {
					return $this->translator->translate('Služby');
				}
				if (strpos($log->message, 'contactForm') !== false) {
					return $this->translator->translate('Kontakt');
				}
				if (strpos($log->message, 'secondContactForm') !== false) {
					return $this->translator->translate('Kontakt');
				}

				return $log->message;
			}
		);

		$grid->addColumnText('name', 'name')->setRenderer(
			function (Log $log): string {
				$data = Json::decode($log->context);
				return $data->context->formData->name ?? '';
			}
		);
		$grid->addColumnText('email', 'email')->setRenderer(
			function (Log $log): string {
				$data = Json::decode($log->context);
				return $data->context->formData->email ?? '';
			}
		);
		$grid->addColumnText('phone', 'phone')->setRenderer(
			function (Log $log): string {
				$data = Json::decode($log->context);
				return $data->context->formData->phone ?? '';
			}
		);
		$grid->addColumnText('text', 'Text')->setRenderer(
			function (Log $log): string {
				$data = Json::decode($log->context);
				return $data->context->formData->text ?? '';
			}
		);
		$grid->addColumnText('mutation', 'mutation')->setRenderer(
			function (Log $log): string {
				$data = Json::decode($log->context);
				if (isset($data->context->mutation)) {
					$mutation = $this->mutationRepository->getById($data->context->mutation);
					if ($mutation !== null) {
						return $mutation->name;
					}
				}
				return '';
			}
		);

		$grid->addColumnDateTime('files', 'files')->setRenderer(
			function (Log $log): string {
				$links = [];
				$data = Json::decode($log->context);
				if (isset($data->context->formData->files)) {
					foreach ($data->context->formData->files as $file) {
						if (file_exists($file->backup)) {
							$pathinfo = pathinfo($file->backup);
							$links[] = '<a target="_blank" href="' . $this->link('download', ['fileName' => $pathinfo['basename']]) . '">' . $pathinfo['basename'] . '</a>';
						}
					}
				}
				return implode('<br>', $links);
			}
		)->setTemplateEscaping(false);


		$grid->addColumnDateTime('createdAt', 'createdAt')->setRenderer(
			function (Log $log): string {
				return $log->createdAt->format('j.n.Y H:i:s');
			}
		);

		$grid->addAction('Detail', 'Detail', ':detail')->setClass('btn btn-xs btn-primary');

		$grid->setTranslator($this->translator);

		return $grid;
	}


	public function handleDownload(string $fileName): void
	{
		$pathToFile = $this->fileLogDirectoryPath . '/' . $fileName;
		$this->presenter->sendResponse(new FileResponse($pathToFile));
	}

}
