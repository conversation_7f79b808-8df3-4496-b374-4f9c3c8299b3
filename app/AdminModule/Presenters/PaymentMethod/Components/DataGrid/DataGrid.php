<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\PaymentMethod\Components\DataGrid;


use App\Model\Orm\DeliveryMethod\DeliveryMethodConfiguration;
use App\Model\Orm\DeliveryMethod\DeliveryMethodPrice;
use App\Model\Orm\PaymentMethod\PaymentMethodConfiguration;
use App\Model\Orm\PaymentMethod\PaymentMethodConfigurationRepository;
use App\Model\Translator;
use Nette\Application\UI\Control;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Ublaboo\DataGrid\Exception\DataGridException;

/**
 * @property-read DefaultTemplate $template
 */
class DataGrid extends Control
{

	public function __construct(
		private readonly Translator $translator,
		private readonly PaymentMethodConfigurationRepository $paymentMethodConfigurationRepository,
	)
	{
	}


	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}


	/**
	 * @throws DataGridException
	 */
	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();

		$grid->setDataSource($this->paymentMethodConfigurationRepository->findAll()->orderBy('mutation'));
		$grid->addColumnText('mutation','label_mutation', 'mutation.name');
		$grid->addColumnText('paymentMethodUniqueIdentifier','payment_method_unique_identifier');
		$grid->addColumnText('name','name');
		$grid->addColumnText('public', 'public')->setReplacement([
			1 => $this->translator->translate('public'),
			0 => $this->translator->translate('non-public'),
		]);
		$grid->addColumnText('price', 'price')->setRenderer(function (PaymentMethodConfiguration $methodConfiguration) {
			if (($methodConfiguration->prices->countStored()) === 0) {
				return '<span style="color:red;">without prices</span>';
			}
			/** @var DeliveryMethodPrice $price */
			$price = $methodConfiguration->prices->toCollection()->getBy([]);

			return $price->price->asMoney();

		})->setTemplateEscaping(false);

		$grid->addAction('edit', 'Edit', ':edit')->setClass('btn btn-xs btn-primary');

		$grid->setTranslator($this->translator);

		return $grid;
	}

}
