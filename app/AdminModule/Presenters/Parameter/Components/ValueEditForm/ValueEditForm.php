<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\Parameter\Components\ValueEditForm;

use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\ParameterValue\ParameterValueModel;
use App\Model\Translator;
use Nette\Application\UI;
use Nette\Utils\ArrayHash;

final class ValueEditForm extends UI\Control
{

	public function __construct(
		private readonly ParameterValue $parameterValue,
		private readonly Translator $translator,
		private readonly ParameterValueModel $parameterValueModel,
	)
	{
	}

	public function render(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->parameterValue = $this->parameterValue;
		$this->template->render(__DIR__ . '/valueEditForm.latte');
	}

	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);
		$form->onSuccess[] = [$this, 'editFormSucceeded'];
		return $form;
	}

	public function editFormSucceeded(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();
		$this->parameterValueModel->saveValueDetail($this->parameterValue, $valuesAll);

	}

}
