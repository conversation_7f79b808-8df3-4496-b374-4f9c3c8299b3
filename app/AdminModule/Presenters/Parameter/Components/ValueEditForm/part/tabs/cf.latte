<div class="c-custom-fields">

	{var $langCode = 'none'}


	<div class="b-std__content"
		 data-controller="CustomFields"
		 data-action="CustomField:updateValue->CustomFields#updateValue CustomFieldImage:updateValue->CustomFields#updateValue CustomFieldList:addListItem->CustomFields#addListItem CustomFieldList:removeListItem->CustomFields#removeListItem CustomFieldList:updateListOrder->CustomFields#updateListOrder"
		 data-customfields-lang-value="{$langCode}"
		 data-customfields-scheme-value="{$parameterValue->getCfSchemeJson()}"
		 data-customfields-values-value='{$parameterValue->getCfContent()}'>

		<div data-customfields-target="content"></div>
		<input type="hidden" data-customfields-target="values" name="customFields" cols="60" rows="20"></input>
	</div>
</div>


