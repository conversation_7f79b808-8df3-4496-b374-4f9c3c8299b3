<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\FileLibrary;

use App\AdminModule\Presenters\BasePresenter;
use App\AdminModule\Presenters\FileLibrary\Components\DataGrid\DataGrid;
use App\AdminModule\Presenters\FileLibrary\Components\DataGrid\DataGridFactory;

final class FileLibraryPresenter extends BasePresenter
{

	public function __construct(
		private readonly DataGridFactory $dataGridFactory,
	)
	{
		parent::__construct();
	}

	protected function createComponentGrid(): DataGrid
	{
		return $this->dataGridFactory->create($this->user->isDeveloper());
	}

}
