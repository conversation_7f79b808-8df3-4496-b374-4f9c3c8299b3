<?php

declare(strict_types=1);

namespace App\Model;

use App\Model\CustomField\LazyValue;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Routable;
use App\Model\Orm\RoutableEntity;
use App\Model\StaticPage\StaticPage;
use App\PostType\MenuMain\Model\Orm\MenuMain\MenuMain;
use App\PostType\MenuMain\Model\Orm\MenuMainLocalization\MenuMainLocalization;
use App\PostType\Page\Model\Orm\CatalogTree;
use Nextras\Orm\Collection\ICollection;
use stdClass;

final class MenuService
{

	public function __construct(
		private readonly Orm $orm,
	) {}

	private function loadMenuItems(int $parent, array $hide): ICollection
	{
		$where = [
			'parent' => $parent,
			'public' => 1,
		];
		if ($hide !== []) {
			$where['uid!='] = $hide;
		}

		return $this->orm->tree->findBy($where)->orderBy('sort');
	}

	public function createMenu(Mutation $mutation, int $parent, Routable|StaticPage|null $object, ICollection|null $menuItemEntities = null, bool $markSelected = true, array $hide = []): array
	{
		$menu = [];
		if (empty($menuItemEntities)) {
			$menuItemEntities = $this->loadMenuItems($parent, $hide);
		}

		if ($object === null) {
			return $menu;
		}

		foreach ($menuItemEntities as $menuItemEntity) {

			if (!($menuItemEntity instanceof MenuMainLocalization)) {
				continue;
			}

			$menuItem = new stdClass();
			$menuItem->active = false;
			$menuItem->selected = false;
			$menuItem->cf = $menuItemEntity->menuMain->cf;
			//			$menuItem->isBig = $menuItemEntity->isBig;
			//			$menuItem->isBold = $menuItemEntity->isBold;

			if (!isset($menuItemEntity->menuMain->cf->link)) {
				continue;
			}

			if ($menuItemEntity->menuMain->cf->link->toggle === 'customHref') {
				$menuItem->isCustomHref = true;
				$menuItem->name = $menuItemEntity->menuMain->cf->link->customHref->hrefName;
				$menuItem->link = $menuItemEntity->menuMain->cf->link->customHref->href;

				$menu[] = $menuItem;

				continue;
			}

			if (!isset($menuItemEntity->menuMain->cf->link->systemHref)) {
				continue;
			}

			if (!isset($menuItemEntity->menuMain->cf->link->systemHref->page->entity)) {
				continue;
			}

			if (!$page = $menuItemEntity->menuMain->cf->link->systemHref->page->entity) {
				continue;
			}

			$menuItem->cf = $menuItemEntity->menuMain->cf;
			$menuItem->page = $page;
			$menuItem->active = $this->isActive($object, $page);
			$menuItem->selected = $this->isSelected($object, $page, $markSelected);

			if ($menuItem->active || $menuItem->selected) {
				$menuItem->submenu = $this->createMenu($mutation, $page->id, $object, null, $markSelected);
			}

			$menu[] = $menuItem;
		}

		return $menu;
	}

	private function isSelected(Routable|StaticPage|null $object, Routable $page, bool $markSelected): bool
	{
		if (!$markSelected) {
			return false;
		}

		if (!($object instanceof Routable)) {
			return false;
		}

		if (!isset($object->path)) {
			return false;
		}

		if (!in_array($page->getId(), $object->path, true) && $page->getId() !== $object->getId()) {
			return false;
		}

		if ($page->getId() !== $object->getId()) {
			return false;
		}

		return true;
	}

	private function isActive(Routable|StaticPage|null $object, Routable $page): bool
	{
		if (!isset($object->path)) {
			return false;
		}

		if (!in_array($page->getId(), $object->path, true) && $page->getId() !== $object->getId()) {
			return false;
		}

		return true;
	}

	public function getUserMenu(Routable|StaticPage $object, bool $markSelected = true, array $items = []): array
	{
		$menu = [];

		if ($items === []) {
			return $menu;
		}

		foreach ($items as $item) {
			$menuItem = new stdClass();
			$menuItem->page = $item;

			if (isset($item->id) && in_array($item->id, (array) $object->getPath(), true)) {
				$menuItem->active = true;
			} else {
				$menuItem->active = false;
			}

			if (isset($item->id) && $markSelected && $item->id == $object->getId()) {
				$menuItem->selected = true;
			} else {
				$menuItem->selected = false;
			}

			$menu[] = $menuItem;
		}
		return $menu;
	}


	//	public function updateActiveForMainMenu(array &$menu): void
	//	{
	//		$active = [];
	//		foreach ($menu as $index => $item){
	//			if (isset($item->menuItem) && isset($item->active) && $item->active) {
	//				$active[$index] = true;
	//			}
	//		}
	//
	//		$i = 1;
	//		foreach ($active as $index => $path){
	//			if ($i < count($active)) {
	//				$menu[$index]->active = false;
	//			}
	//			$i++;
	//		}
	//	}

}
