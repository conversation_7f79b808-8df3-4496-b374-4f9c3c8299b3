<?php declare(strict_types=1);

namespace App\Model\Form;

use App\Model\Mutation\MutationHolder;
use Contributte\Monolog\LoggerManager;
use Nette\Application\UI\Presenter;
use Nette\Forms\Controls\UploadControl;
use Nette\Forms\Form;
use Nette\Forms\Controls\TextInput;

class FormDataLogger
{
	const FORM_DATA_KEY = 'formData';

	public function __construct(
		private readonly LoggerManager $loggerManager,
		private readonly MutationHolder $mutationHolder,
	)
	{
	}

	public function logData(Form $form, array $data): void
	{
		// Použít aktuální hodnoty z formuláře místo parametru $data
		$currentData = (array) $form->values;

		bd("FormDataLogger - original data:");
		bd($data);
		bd("FormDataLogger - current form values:");
		bd($currentData);

		$logData = $this->obfuscatePasswords($form, $currentData);
		$logData = $this->skipFileUploads($form, $logData);
		$this->loggerManager->get('formDataSaver')->info(
			$form->lookupPath(Presenter::class, throw: false) ?? $form->getName(),
			[
				self::FORM_DATA_KEY => $logData,
				'mutation' => $this->mutationHolder->getMutation()->id,
			]
		);
	}

	public function obfuscatePasswords(Form $form, array $data): array
	{
		$components = $form->getComponents(true, TextInput::class);
		foreach ($components as $textInput) {
			assert($textInput instanceof TextInput);
			if ($textInput->getControl()->type === 'password') {
				$basePath = preg_replace('/^' . $form->lookupPath() . '-/', '', $textInput->lookupPath());

				$temp =& $data;
				foreach(explode('-', $basePath) as $key) {
					$temp =& $temp[$key];
				}
				$temp = '******';
			}
		}
		return (array) $data; // force array (phpstorm is confused)
	}

	public function skipFileUploads(Form $form, array $data): array
	{
		$components = $form->getComponents(true, UploadControl::class);
		foreach ($components as $uploadInput) {
			assert($uploadInput instanceof UploadControl);
			unset($data[$uploadInput->getName()]);
			$data[$uploadInput->getName()] = 'filled';
		}
		return (array) $data; // force array (phpstorm is confused)
	}

}
