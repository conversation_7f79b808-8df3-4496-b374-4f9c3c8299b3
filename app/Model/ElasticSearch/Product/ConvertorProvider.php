<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Product;

use App\Model\ElasticSearch\Product\Convertor\BaseData;
use App\Model\ElasticSearch\Product\Convertor\CategoryData;
use App\Model\ElasticSearch\Product\Convertor\ParameterData;
use App\Model\ElasticSearch\Product\Convertor\PriceData;
use App\Model\ElasticSearch\Product\Convertor\StoreData;
use App\Model\ElasticSearch\Product\Convertor\TopScoreData;
use LogicException;

class ConvertorProvider
{

	private array $map;

	public function __construct(
		BaseData $baseData,
		CategoryData $categoryData,
		ParameterData $parameterData,
		PriceData $priceData,
		StoreData $storeData,
		TopScoreData $topScoreData,
	)
	{
		$this->map = [];
		foreach (func_get_args() as $convertor) {
			$this->map[$convertor::class] = $convertor;
		}
	}


	public function get(string $class): Convertor
	{
		return $this->map[$class] ?? throw new LogicException(sprintf("Missing convertor for '%s' class", $class));
	}


	public function getAll(): array
	{
		return array_values($this->map);
	}


	public function getAllLikeStrings(): array
	{
		return array_map(fn(object $item) => $item::class, $this->getAll());
	}

}
