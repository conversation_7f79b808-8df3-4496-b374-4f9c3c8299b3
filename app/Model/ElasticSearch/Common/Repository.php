<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Common;

use App\Model\ElasticSearch\IndexModel;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\Mutation\Mutation;
use App\PostType\Page\Model\Orm\Tree;
use Elastica\Query;
use Elastica\Query\Range;
use Elastica\QueryBuilder;
use Elastica\ResultSet;

class Repository extends \App\Model\ElasticSearch\Repository
{

	public function __construct(
		private readonly EsIndexRepository $esIndexRepository,
		IndexModel $indexModel,
	)
	{
		parent::__construct($indexModel);
	}


	public function fulltextSearch(Mutation $mutation, array $must, string $q, int $size = 30, array $inPaths = [], int $offset = 0): ResultSet
	{
		$query = new Query();
		$query->setSize($size)
			->setFrom($offset);

		$fields = [];
		$fields[] = 'name.dictionary^80';
		$fields[] = 'nameAnchor.dictionary^80';
		$fields[] = 'nameTitle.dictionary^80';
		$fields[] = 'annotation.dictionary^40';
		$fields[] = 'content.dictionary^20';
		$fields[] = 'name^20';
		$fields[] = 'nameTitle^20';
		$fields[] = 'nameAnchor^20';
		$fields[] = 'tagsToSearch^60';

		$b = new QueryBuilder();

		foreach ($inPaths as $inPath) {
			$must[] = $this->addInPath($inPath);
		}

		$publicFrom = new Range('publicFrom', [
			'lte' => 'now',
		]);
		$must[] = $publicFrom;

		$publicTo = new Range('publicTo', [
			'gte' => 'now',
		]);
		$must[] = $publicTo;

		$must[] = $b->query()->multi_match()
			->setType('best_fields') //phrase_prefix phrase, cross_fields best_fields
			->setQuery($q)
			->setFuzziness(3)
			->setOperator('OR')
			->setFields($fields);

		$filterBuilder = $b->query()->bool();
		foreach ($must as $item) {
			$filterBuilder->addMust($item);
		}

		$query->setQuery($filterBuilder);

		$esIndex = $this->esIndexRepository->getCommonLastActive($mutation);
		return $this->baseSearch($esIndex, $query);
	}



	private function addInPath(Tree $tree): Query\Term
	{
		return new Query\Term(['path' => $tree->id]);
	}

}
