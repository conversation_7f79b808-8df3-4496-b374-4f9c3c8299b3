<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Common\Convertor;

use App\Model\ElasticSearch\Common\Convertor;
use App\Model\ElasticSearch\ConvertorHelper;
use App\PostType\Material\Model\Orm\MaterialLocalization;

class MaterialData implements Convertor
{

	public const TYPE = 'material';

	public function convert(object $object): array
	{
		assert($object instanceof MaterialLocalization);

		$data = [
			'id' => $object->id,
			'public' => (bool) $object->public,
			'name' => $object->name,
			'nameTitle' => $object->nameTitle,
			'nameAnchor' => $object->nameAnchor,
			'content' => $object->getEsContent(),
			'type' => self::TYPE,
			'annotation' => $object->annotation,
			'publicFrom' => ConvertorHelper::convertTime($object->publicFrom),
			'publicTo' => ConvertorHelper::convertTime($object->publicTo),
		];

		$data['tagsToSearch'] = [];
		if ($object->materialTags->count()) {
			foreach ($object->materialTags as $tag) {
				if ($tag->isPublished()) {
					$data['tagsToSearch'][] = $tag->name;
				}
			}
		}

		return $data;
	}

}
