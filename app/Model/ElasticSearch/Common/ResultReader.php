<?php declare(strict_types = 1);

namespace App\Model\ElasticSearch\Common;

use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Feature\Model\Orm\FeatureLocalization;
use App\PostType\Feature\Model\Orm\FeatureLocalizationRepository;
use App\PostType\Material\Model\Orm\MaterialLocalizationRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\PostType\Reference\Model\Orm\ReferenceLocalizationRepository;
use Elastica\ResultSet;
use IMAP\Connection;
use Nette\Utils\ArrayList;
use Nextras\Orm\Collection\ArrayCollection;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;

class ResultReader
{

	public function __construct(
		private TreeRepository $treeRepository,
		private BlogLocalizationRepository $blogLocalizationRepository,
		private MaterialLocalizationRepository $materialLocalizationRepository,
		private FeatureLocalizationRepository $featureLocalizationRepository,
		private ReferenceLocalizationRepository $referenceLocalizationRepository,
		private AuthorLocalizationRepository $authorLocalizationRepository,
	)
	{
	}


	public function mapResultToEntityCollection(ResultSet $result, string $type, float $minScore = 0.01): ICollection
	{
		$ids = [];
		foreach ($result->getResults() as $result) {
			if ($type === ElasticCommon::TYPE_TREE) {
				if ($result->getScore() > $minScore) {
					$ids[] = (int) str_replace(ElasticCommon::TYPE_TREE . '-', '', $result->getId());
				}
			} elseif ($type === ElasticCommon::TYPE_BLOG) {
				if ($result->getScore() > $minScore) {
					$ids[] = (int) str_replace(ElasticCommon::TYPE_BLOG . '-', '', $result->getId());
				}
			}
		}

		if ($ids !== []) {
			if ($type === ElasticCommon::TYPE_TREE) {
				return $this->treeRepository->findFilteredPages($ids);
			} elseif ($type === ElasticCommon::TYPE_BLOG) {
				return $this->blogLocalizationRepository->findFiltered($ids);
			}
		}

		return new EmptyCollection();
	}


	public function mapResultToEntityCollectionUniversal(ResultSet $result, float $minScore = 0.01): array
	{
		$data = [];
		foreach ($result->getResults() as $result) {
			if (strpos($result->getId(), ElasticCommon::TYPE_TREE) !== false) {
				if ($result->getScore() > $minScore) {
					$id = (int)str_replace(ElasticCommon::TYPE_TREE . '-', '', $result->getId());
					$data[] = $this->treeRepository->getById($id);

				}
			}
			if (strpos($result->getId(), ElasticCommon::TYPE_BLOG) !== false) {
				if ($result->getScore() > $minScore) {
					$id = (int)str_replace(ElasticCommon::TYPE_BLOG . '-', '', $result->getId());
					$data[] = $this->blogLocalizationRepository->getById($id);

				}
			}

			if (strpos($result->getId(), ElasticCommon::TYPE_MATERIAL) !== false) {
				if ($result->getScore() > $minScore) {
					$id = (int)str_replace(ElasticCommon::TYPE_MATERIAL . '-', '', $result->getId());
					$data[] = $this->materialLocalizationRepository->getById($id);

				}
			}

			if (strpos($result->getId(), ElasticCommon::TYPE_FEATURE) !== false) {
				if ($result->getScore() > $minScore) {
					$id = (int)str_replace(ElasticCommon::TYPE_FEATURE . '-', '', $result->getId());
					$data[] = $this->featureLocalizationRepository->getById($id);

				}
			}

			if (strpos($result->getId(), ElasticCommon::TYPE_REFERENCE) !== false) {
				if ($result->getScore() > $minScore) {
					$id = (int)str_replace(ElasticCommon::TYPE_REFERENCE . '-', '', $result->getId());
					$data[] = $this->referenceLocalizationRepository->getById($id);

				}
			}

			if (strpos($result->getId(), ElasticCommon::TYPE_AUTHOR) !== false) {
				if ($result->getScore() > $minScore) {
					$id = (int)str_replace(ElasticCommon::TYPE_AUTHOR . '-', '', $result->getId());
					$data[] = $this->authorLocalizationRepository->getById($id);

				}
			}

		}

		return $data;

//		if ($ids !== []) {
//			if ($type === ElasticCommon::TYPE_TREE) {
//				return $this->treeRepository->findFilteredPages($ids);
//			} elseif ($type === ElasticCommon::TYPE_BLOG) {
//				return $this->blogLocalizationRepository->findFiltered($ids);
//			}
//		}

//		return new EmptyCollection();
	}

}
