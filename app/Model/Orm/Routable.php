<?php declare(strict_types = 1);

namespace App\Model\Orm;

use App\Model\Orm\Alias\Alias;
use App\Model\Orm\Mutation\Mutation;

interface Routable
{

	public function getId(): int;

	public function getNameTitle(): string;
	public function setNameTitle(string $nameTitle): void;

	public function getNameAnchor(): string;
	public function setNameAnchor(string $nameAnchor): void;

	public function getDescription(): string;
	public function setDescription(string $description): void;


	public function getKeywords(): string;
	public function setKeywords(string $keywords): void;

	public function getAliasEntity(): Alias|null;

	public function getAlias(): string;
	public function setAlias(string $alias): void;

	public function getAliasHistoryString(): string;
	public function setAliasHistoryString(string $aliasHistory): void;

	public function getPath(): array;

	public function getMutation(): Mutation;

}
