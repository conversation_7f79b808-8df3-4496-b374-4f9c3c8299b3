<?php declare(strict_types=1);

namespace App\Model\Orm\Log;


use Nette\Utils\ArrayHash;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $message {default ''}
 * @property string $context {default '{}'}
 * @property DateTimeImmutable $createdAt {default now}
 *
 *  VIRTUAL
 * @property-read  ArrayHash|null $formData {virtual}
 */
class Log extends Entity
{
	protected function getterFormData(): ?ArrayHash
	{
		if ($this->context === '{}') {
			return null;
		}
		$context = json_decode($this->context, true, 512, JSON_THROW_ON_ERROR);
		return $context['context']['formData'] ? ArrayHash::from($context['context']['formData']) : null;
	}
}
