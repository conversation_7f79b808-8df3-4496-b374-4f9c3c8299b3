<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductVariant;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ParameterValue\ParameterValue;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductFile\ProductFile;
use App\Model\Orm\ProductImage\ProductImage;
use App\Model\Orm\ProductReview\ProductReview;
use App\Model\Orm\ProductTree\ProductTree;
use App\Model\Orm\ProductVariant\Availability\AbstractProductAvailability;
use App\Model\Orm\ProductVariant\Availability\ProductAvailability;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalization;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use App\Model\Orm\State\State;
use App\Model\Orm\Stock\Stock;
use App\Model\Orm\Supply\Supply;
use App\Model\Orm\Traits\HasCache;
use App\Model\Orm\Traits\HasTranslator;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\Model\VatCalculator;
use ArrayIterator;
use Brick\Money\Currency;
use Brick\Money\Money;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Relationships\IRelationshipCollection;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $ean {default 0}
 * @property string $code {default ''}
 * @property DateTimeImmutable|null $created {default 'now'}
 * @property int|null $createdBy
 * @property DateTimeImmutable|null $edited {default 'now'}
 * @property int|null $editedBy
 * @property int $sort {default 0}
 * @property int $soldCount {default 0}
 * @property int|null $isInDiscount
 * @property string|null $extId
 *
 *
 * RELATIONS
 * @property Product $product {m:1 Product::$variants}
 * @property ParameterValue|null $param1Value {m:1 ParameterValue::$variants1}
 * @property ParameterValue|null $param2Value {m:1 ParameterValue::$variants2}
 * @property OneHasMany|Supply[] $supplies {1:m Supply::$variant, cascade=[persist, remove]}
 * @property ProductVariantLocalization[]|OneHasMany $variantLocalizations {1:m ProductVariantLocalization::$variant, orderBy=[id=ASC], cascade=[persist, remove]}
 * @property ProductVariantPrice[]|OneHasMany $prices {1:m ProductVariantPrice::$productVariant, cascade=[persist, remove]}
 *
 *
 * VIRTUAL
 * @property-read string|null $cf {virtual}
 * @property-read array|null $path {virtual}
 * @property-read string $stringId {virtual}
 * @property-read string $name {virtual}
 * @property-read string $nameAnchor {virtual}
 * @property-read string $nameTitle {virtual}
 * @property-read string $nameVariant {virtual}
 * @property-read string $nameVariantFormat {virtual}
 * @property-read string|null $content {virtual}
 *
 * @property-read int|null $param1Id {virtual}
 * @property-read int|null $param2Id {virtual}
 * @property-read int|null $param1ValueId {virtual}
 * @property-read int|null $param2ValueId {virtual}
 * @property-read int|null $paramValueIds {virtual}
 * @property-read string|null $template {virtual}
 * @property-read string|null $keywords {virtual}
 * @property-read string|null $description {virtual}
 * @property-read string|null $annotation {virtual}
 * @property-read ProductImage|null $firstImage {virtual}
 * @property-read string|null $uid {virtual}
 * @property-read bool $active {virtual}
 *
 * @property-read array $pricesByLevel {virtual}
 *
 * @property-read bool $isVariant {virtual}
 * @property-read bool $isShell {virtual}
 *
 * @property-read int|null $isOld {virtual}
 * @property-read int $isNew {virtual}
 * @property-read bool $isInPrepare {virtual}
 * @property-read bool $isFreeTransport {virtual}
 * @property-read bool $notSoldSeparately {virtual}
 *
 * @property-read array $suppliesByStock {virtual} sklady strukturovane podle ID
 * @property-read array $suppliesByStockAlias {virtual} sklady strukturovane podle aliasu skladu
 * @property-read bool $isInStock {virtual} globalne skladem
 * @property-read bool $isInStockDefault {virtual} skladem na centralnim skladu
 * @property-read bool $isInStockSupplier {virtual} skladem u dodavatelu
 * @property-read int $totalSupplyCount {virtual} pocet skladem celkem
 * @property-read int $suplyCountStockDefault {virtual} pocet skladem na centralnim skladu
 * @property-read int $suplyCountStockSupplier {virtual} pocet skladem u dodavatelu
 *
 * @property-read ProductReview[]|OneHasMany $reviews {virtual}
 * @property-read ProductFile[]|OneHasMany $files {virtual}
 * @property-read ProductTree[]|OneHasMany $productTrees {virtual}
 * @property-read ParameterValue[]|ManyHasMany $parametersValues {virtual}
 * @property-read Tree[]|null $pages {virtual}
 * @property-read Product[]|null $products {virtual}
 * @property-read ProductImage[]|ArrayIterator $images {virtual}
 *
 *
 * @property-read string $cacheId {virtual}
 * @property-read Product[]|ICollection $accessories {virtual}
 * @property-read CatalogTree|null $mainCategory {virtual}
 *
 * @property-read string|null $editedString {virtual}
 * @property-read ProductAvailability $productAvailability {virtual}
 */
class ProductVariant extends BaseEntity
{

	use HasCache;
	use HasTranslator;

	public const SHELL_STRING_ID = '0-0';


	protected function getterActive(): bool
	{
		return (bool) $this->getLocalization(
			$this->product->getMutation()
		)->active;
	}

	public function getterStringId(): string
	{
		if (!isset($this->cache['stringId'])) {
			$stringId = [];
//			bd($this->param1Value);
			if ($this->param1Value) {
				$stringId[] = $this->param1Value->id;
			} else {
				$stringId[] = 0;
			}

			if ($this->param2Value) {
				$stringId[] = $this->param2Value->id;
			} else {
				$stringId[] = 0;
			}

			$this->cache['stringId'] = implode('-', $stringId);
		}

		return $this->cache['stringId'];
	}


	protected function getterUid(): ?string
	{
		return $this->product->uid;
	}

	// ************************************ Name ************************************************

	public function getterNameVariant(): string
	{
		if (!isset($this->cache['nameVariant'])) {
			$this->cache['nameVariant'] = $this->param1ValueId ? Strings::firstLower($this->translator->translate('pvalue_' . $this->param1ValueId)) : '';
		}

		return $this->cache['nameVariant'];
	}


	public function getterNameVariantFormat(): string
	{
		return $this->nameVariant ? sprintf(' (%s)', $this->nameVariant) : '';
	}


	protected function getterName(): string
	{
		return $this->product->name . $this->nameVariantFormat;
	}


	protected function getterNameAnchor(): string
	{
		return $this->product->nameAnchor . $this->nameVariantFormat;
	}


	protected function getterNameTitle(): string
	{
		return $this->product->nameTitle . $this->nameVariantFormat;
	}

	// ************************************ Param ************************************************

	public function getterParam1Id(): ?int
	{
		return $this->param1ValueId;
	}


	public function getterParam1ValueId(): ?int
	{
		return $this->param1Value ? $this->param1Value->id : null;
	}


	public function getterParam2Id(): ?int
	{
		return $this->param2ValueId;
	}


	public function getterParam2ValueId(): ?int
	{
		return $this->param2Value ? $this->param2Value->id : null;
	}


	protected function getterParamValueIds(): array
	{
		$ret = [];
		if (isset($this->param1Value->id)) {
			$ret[] = $this->param1Value->id;
		}

		if (isset($this->param2Value->id)) {
			$ret[] = $this->param2Value->id;
		}

		return $ret;
	}

	// ************************************************************************************

	protected function getterEditedString(): string
	{
		return $this->edited ? $this->edited->format('Y-m-d H:i:s') : '';
	}


	protected function getterIsShell(): bool
	{
		return $this->stringId === self::SHELL_STRING_ID;
	}


	protected function getterPath(): array
	{
		return $this->product->path;
	}


	protected function getterTemplate(): string
	{
		return $this->product->template;
	}


	protected function getterKeywords(): ?string
	{
		return $this->product->keywords;
	}


	protected function getterDescription(): ?string
	{
		return $this->product->description;
	}


	protected function getterAnnotation(): ?string
	{
		return $this->product->annotation;
	}


	protected function getterMainCategory(): ?CatalogTree
	{
		return $this->product->mainCategory;
	}

	//TODO REF VOJTA ??
	protected function getterFirstImage(): ?ProductImage
	{
		//return $this->product->firstImage;
		return $this->images->current();
	}


	/**
	 * nalezeni obrazku pro danou variantu
	 * @return ArrayIterator<int, ProductImage>
	 */
	protected function getterImages(): ArrayIterator
	{
		$images = $this->product->images;
		$finalImages = [];
		foreach ($images as $i) {
			$toSearch = empty($i->variants) ? [] : explode('|', $i->variants);
			if (in_array($this->id, $toSearch)) {
				$finalImages[] = $i;
			} elseif ($i->variants === null || !$i->variants) {
				$finalImages[] = $i;
			}
		}

		return new ArrayIterator($finalImages);
	}



	// ************************************ Stock ************************************************


	protected function getterSuppliesByStock(): array
	{
		if (!isset($this->cache['suppliesByStock'])) {
			$supplies = [];
			foreach ($this->supplies as $supply) {
				$supplies[$supply->stock->id] = $supply;
			}

			$this->cache['suppliesByStock'] = $supplies;
		}

		return $this->cache['suppliesByStock'];
	}

	protected function getterSuppliesByStockAlias(): array
	{
		if (!isset($this->cache['suppliesByStockAlias'])) {
			$supplies = [];
			foreach ($this->suppliesByStock as $supply) {
				$supplies[$supply->stock->alias] = $supply;
			}

			$this->cache['suppliesByStockAlias'] = $supplies;
		}

		return $this->cache['suppliesByStockAlias'];
	}


	protected function getterTotalSupplyCount(): int
	{
		if (!isset($this->cache['totalSupplyCount'])) {
			$totalSupplyCount = 0;
			foreach ($this->suppliesByStock as $stockId => $supply) {
				$totalSupplyCount += $supply->amount;
			}

			$this->cache['totalSupplyCount'] = $totalSupplyCount;
		}

		return $this->cache['totalSupplyCount'];
	}


	protected function getterIsInStock(): bool
	{
		return $this->totalSupplyCount > 0;
	}

	// **** specificke zkratky pro konkretni sklady *******

	protected function getterIsInStockDefault(): bool
	{
		return !empty($this->suppliesByStockAlias[Stock::ALIAS_SHOP]->amount);
	}

	protected function getterSuplyCountStockDefault(): int
	{
		return $this->isInStockDefault ? $this->suppliesByStockAlias[Stock::ALIAS_SHOP]->amount : 0;
	}

	protected function getterIsInStockSupplier(): bool
	{
		return !empty($this->suppliesByStockAlias[Stock::ALIAS_SUPPLIER_STORE]->amount);
	}

	protected function getterSuplyCountStockSupplier(): int
	{
		return $this->isInStockSupplier ? $this->suppliesByStockAlias[Stock::ALIAS_SUPPLIER_STORE]->amount : 0;
	}

	// ************************************ Price ************************************************


	protected function getterPricesByLevel(): array
	{
		if (!isset($this->cache['pricesByLevel'])) {
			$prices = [];
			foreach ($this->prices as $price) {
				$prices[$price->mutation->id][$price->priceLevel->id] = $price;
			}

			$this->cache['pricesByLevel'] = $prices;
		}

		return $this->cache['pricesByLevel'];
	}


	private function fillPriceCache(Mutation $mutation, PriceLevel $priceLevel, State $state): void
	{
		if (!isset($this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id])) {

			$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id] = Money::zero($mutation->currency);

			if (isset($this->pricesByLevel[$mutation->id][$priceLevel->id])) {
				/** @var ProductVariantPrice $productVariantPrice */
				$productVariantPrice = $this->pricesByLevel[$mutation->id][$priceLevel->id];
				$this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id] = $productVariantPrice->price->asMoney();
			}
		}
	}


	public function price(Mutation $mutation, PriceLevel $priceLevel, State $state, ?Currency $selectedCurrency = null): Money  // $selectedCurrency -> compatibility with other projects, in SA not used
	{
		$this->fillPriceCache($mutation, $priceLevel, $state);
		return $this->cache['priceCache'][$mutation->id][$priceLevel->id][$state->id];
	}


	public function priceVat(Mutation $mutation, PriceLevel $priceLevel, State $state, ?Currency $selectedCurrency = null): Money // $selectedCurrency -> compatibility with other projects, in SA not used
	{
		$basePrice = $this->price($mutation, $priceLevel, $state);
		$vatRate = $this->product->vatRate($state);
		return VatCalculator::priceWithVat($basePrice, $vatRate);
	}


	// ***********************************************************************************


	protected function getterContent(): ?string
	{
		return $this->product->content;
	}


	protected function getterFiles(): IRelationshipCollection
	{
		return $this->product->files;
	}




	protected function getterPages(): ICollection
	{
		return $this->product->pages;
	}




	protected function getterProducts(): ICollection
	{
		return $this->product->products;
	}


	protected function getterIsOld(): ?int
	{
		return $this->product->isOld;
	}


	protected function getterIsNew(): int
	{
		return $this->product->isNew;
	}


	protected function getterIsInPrepare(): ?int
	{
		return $this->product->isInPrepare;
	}


	protected function getterNotSoldSeparately(): ?int
	{
		return $this->product->notSoldSeparately;
	}


	public function getParameterValueByUid(string $parameterUid): mixed
	{
		return $this->product->getParameterValueByUid($parameterUid);
	}


	public function getParameters(): array
	{
		return $this->product->getParameters();
	}

	public function getterAccessories(): ICollection
	{
		return $this->product->accessories;
	}


	public function getterIsVariant(): bool
	{
		return true;
	}


	protected function getterCacheId(): string
	{
		return 'var' . $this->id;
	}


	protected function getterIsFreeTransport(): bool
	{
		return (bool) $this->product->isFreeTransport;
	}


	protected function getterCf(): mixed
	{
		return $this->product->cf;
	}


	public function getLocalization(Mutation $mutation): ?ProductVariantLocalization
	{
		return $this->variantLocalizations->toCollection()->getBy(['mutation' => $mutation]);
	}

	public function getLocalizationChecked(Mutation $mutation): ProductVariantLocalization
	{
		$variantLocalization = $this->getLocalization($mutation);
		if ($variantLocalization === null) {
			throw new \LogicException(sprintf('Missing variant localization for variantId %d in \'%s\' mutation', $this->id, $mutation->id));
		}

		return $variantLocalization;
	}


	public function getFormId(): string
	{
		return ($this->isPersisted()) ? (string) $this->id : 'newItem';
	}

	public function getId(): int
	{
		return $this->id;
	}

	protected function getMutation(): Mutation
	{
		// TODO
		throw new \LogicException('ProductVariant must not be Routable, ProductVariantLocalization should be Routable instead.');
	}

	public function getterProductAvailability(): AbstractProductAvailability
	{
		if (!isset($this->cache['availability'])) {
			$availability = clone $this->product->productAvailability;
			$this->cache['availability'] = $availability->setProductVariant($this);
		}
		return $this->cache['availability'];
	}

}
