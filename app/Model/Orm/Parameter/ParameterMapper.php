<?php

declare(strict_types=1);

namespace App\Model\Orm\Parameter;

use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasCamelCase;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Mapper\Dbal\Conventions\Conventions;
use Nextras\Orm\Mapper\Dbal\Conventions\IConventions;
use Nextras\Orm\Mapper\Dbal\DbalMapper;

final class ParameterMapper extends DbalMapper
{

	use HasCamelCase;

	protected $tableName = 'parameter';

	// add mapping
	protected function createConventions(): IConventions
	{
		$conventions = parent::createConventions();
		assert($conventions instanceof Conventions);
		$conventions->manyHasManyStorageNamePattern = '%s_%s';

		return $conventions;
	}


	public function searchByName(string $search, array $selectedSiblingsIds): ICollection
	{
		$builder = $this->builder()
			->andWhere('name LIKE %_like_', $search);

		if ($selectedSiblingsIds) {
			$builder->andWhere('id not in %i[]', $selectedSiblingsIds);
		}

		return $this->toCollection($builder);
	}


	public function findByUidOrdered(array $uids): ICollection
	{
		$tmpUids = array_map(function ($uid) {
			return "'" . $uid . "'";
		}, $uids);
		$builder = $this->builder()
			->andWhere('uid in %s[]', $uids)
			->orderBy('%raw', 'FIELD(uid, ' . implode(',', $tmpUids) . ')');
		return $this->toCollection($builder);
	}


	public function findProductParameterValueIds(Parameter $parameter): Result
	{
		$builder = $this->builder();
		$builder->select('parameterValueId')->from('product_parameter')
			->andWhere('parameterId = %i', $parameter->id);

		return $this->connection->queryByQueryBuilder($builder);
	}


	public function findParametersForProduct(Product $product): ICollection
	{
		$builder = $this->builder();
		$builder->select('p.*')
			->from('product_parameter as pp')
			->joinInner('[parameter] as p', '[p.id] = [pp.parameterId]')
			->andWhere('productId = %i', $product->id)
			->orderBy('p.sort')
			->groupBy('parameterId');

		return $this->toCollection($builder);
	}


	public function findByExactOrder(array $ids): ICollection
	{
		$builder = $this->builder()
			->andWhere('id in %i[]', $ids)
			->orderBy('%raw', 'FIELD(id, ' . implode(',', $ids) . ')');

		return $this->toCollection($builder);
	}

}
