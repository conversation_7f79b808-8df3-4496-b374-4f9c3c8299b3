<?php declare(strict_types = 1);

namespace App\Model\Orm\Parameter;

use App\Model\Orm\CollectionById;
use App\Model\Orm\Product\Product;
use App\Model\Orm\Traits\HasSimpleSave;
use Nextras\Dbal\Result\Result;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Parameter|null getById($id)
 * @method Parameter|null getBy(array $conds)
 * @method Parameter[]|ICollection searchByName(string $search, array $selectedSiblingsIds)
 * @method Parameter[]|ICollection findByUidOrdered(array $uids)
 * @method Parameter[]|ICollection findByExactOrder(array $ids)
 *
 * @method Result findProductParameterValueIds(Parameter $parameter)
 * @method Parameter[]|ICollection findParametersForProduct(Product $product)
 * @method Parameter save(?Parameter $entity, array $data)
 */
final class ParameterRepository extends Repository implements CollectionById
{

	use HasSimpleSave;

	/**
	 * @inheritDoc
	 */
	public static function getEntityClassNames(): array
	{
		return [Parameter::class];
	}


	/**
	 * @param string|int|null $id
	 */
	public function getByExtId($id): ?Parameter
	{
		return isset($id) ? $this->getBy(['extId' => (string) $id]) : null;
	}

	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}
}
