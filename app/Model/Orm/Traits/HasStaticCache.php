<?php

declare(strict_types=1);

namespace App\Model\Orm\Traits;

use Closure;
use Nette\Utils\Json;
use Nextras\Orm\Entity\IEntity;

trait HasStaticCache {

	protected static array $staticCache = [];
	/**
	 * @phpstan-param  Closure(): (mixed) $generator
	 */
	protected function loadCache(string $key, callable $generator): mixed
	{
		$data = static::$staticCache[$key] ?? null;

		if ($data === null && is_callable($generator)) {
			try {
				$data = $generator();
				static::$staticCache[$key] = $data;
			} catch (\Throwable $e) {
				unset(static::$staticCache[$key]);
			}
		}

		return $data;
	}

	protected function createCacheKey(mixed ...$args): string
	{
		$toJson = [];
		$prefix = '';

		if (is_string($args[0])) {
			$prefix = $args[0];
		}

		foreach ($args as $arg) {
			if ($arg instanceof IEntity) {
				$toJson[] = ['entity' => $arg::class, 'id' => $arg->getRawValue('id')];
			} else {
				$toJson[] = $arg;
			}
		}

		return $prefix.md5(Json::encode($toJson));
	}
}
