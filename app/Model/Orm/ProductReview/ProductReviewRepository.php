<?php

declare(strict_types=1);

namespace App\Model\Orm\ProductReview;

use App\Model\Orm\Product\Product;
use Nextras\Orm\Repository\Repository;

/**
 * @method ProductReview getById($id)
 * @method array getStatistic($id)
 * @method array getProductForReviewFormOrder($id)
 */
final class ProductReviewRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [ProductReview::class];
	}

	public function getMainReview(Product $product, int $userId): ?ProductReview
	{
		return $this->getBy(['product' => $product->id, 'userId' => $userId]);
	}

}
