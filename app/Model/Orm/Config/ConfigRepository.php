<?php declare(strict_types = 1);

namespace App\Model\Orm\Config;

use Nextras\Orm\Repository\Repository;
use App\Model\Orm\Traits\HasSimpleSave;

/**
 * @method Config|null getById($id)
 * @method Config|null getBy(array $conds)
 * @method Config save(?Config $entity, array $data)
 */
final class ConfigRepository extends Repository
{

	use HasSimpleSave;

	/**
	 * @inheritDoc
	 */
	public static function getEntityClassNames(): array
	{
		return [Config::class];
	}

	public function getByUid(string $uid, ?string $subUid = null): ?Config
	{
		return $this->getBy(['uid' => $uid, 'subUid' => $subUid]);
	}

}
