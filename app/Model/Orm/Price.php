<?php declare(strict_types = 1);

namespace App\Model\Orm;

use Brick\Money\Money;
use Nextras\Orm\Entity\Embeddable\Embeddable;

/**
 * @property-read string $amount
 * @property-read string $currency
 */
final class Price extends Embeddable
{

	private function __construct(
		string $amount,
		string $currency,
	)
	{
		parent::__construct([
			'amount' => $amount,
			'currency' => $currency,
		]);
	}

	public static function from(Money $money): self
	{
		return new self(
			(string) $money->getAmount(),
			$money->getCurrency()->getCurrencyCode(),
		);
	}

	public function asMoney(): Money
	{
		return Money::of(
			$this->amount,
			$this->currency,
		);
	}

}
