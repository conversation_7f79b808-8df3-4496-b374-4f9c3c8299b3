<?php declare(strict_types = 1);

namespace App\Model\Orm\Alias;

use App\Model\Orm\CollectionById;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Alias getById($id)
 * @method Alias[]|ICollection findByName($string)
 * @method Alias[]|ICollection findByExactOrder(array $ids)
 */
final class AliasRepository extends Repository implements CollectionById
{

	public static function getEntityClassNames(): array
	{
		return [Alias::class];
	}


	public function findByIdOrder(array $ids): ICollection
	{
		return $this->findByExactOrder($ids);
	}

}
