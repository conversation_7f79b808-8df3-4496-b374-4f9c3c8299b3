<?php

declare(strict_types=1);

namespace App\Model\Orm\DeliveryMethod;

use function array_values;
use function sprintf;

final class DeliveryMethodRegistry
{
	/**
	 * @var array<string, DeliveryMethod>
	 */
	private array $deliveryMethods = [];

	/**
	 * @param DeliveryMethod[] $deliveryMethods
	 */
	public function __construct(
		array $deliveryMethods,
	) {
		foreach ($deliveryMethods as $deliveryMethod) {
			$this->deliveryMethods[$deliveryMethod->getUniqueIdentifier()] = $deliveryMethod;
		}
	}

	/**
	 * @return list<DeliveryMethod>
	 */
	public function list(): array
	{
		return array_values($this->deliveryMethods);
	}

	public function get(string $uniqueIdentifier): DeliveryMethod
	{
		return $this->deliveryMethods[$uniqueIdentifier] ?? throw new \InvalidArgumentException(sprintf('Delivery method with ID "%s" not found.', $uniqueIdentifier));
	}
}
