<?php declare(strict_types=1);

namespace App\Model\Orm\LibraryImage;

use App\Model\Image\ImageObject;
use App\Model\Orm\ImageEntity;
use App\Model\Orm\LibraryTree\LibraryTree;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ProductImage\ProductImage;
use App\Model\Orm\Traits\HasCache;
use App\Model\Orm\Traits\HasImageResizer;
use Nette\Utils\ArrayHash;
use App\Model\Orm\JsonContainer;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Relationships\ManyHasOne;

/**
 * @property int $id {primary}
 * @property string $name
 * @property string $filename
 * @property int|null $sort
 * @property DateTimeImmutable|null $timeOfChange
 * @property string|null $sourceImage
 * @property string|null $md5
 * @property ArrayHash $alts {container JsonContainer}
 * @property int $width {default 0}
 * @property int $height {default 0}
 *
 * @property LibraryTree|null $library {m:1 LibraryTree::$images}
 * @property ProductImage[]|ManyHasOne $productImages  {1:m ProductImage::$libraryImage}
 *
 * VIRTUAL
 * @property-read  string|null $url {virtual}
 * @property-read  string|null $ext {virtual}
 * @property-read  string|null $fileNameWithoutExtension {virtual}

 * @property-read  bool $isSvg {virtual}
 */
class LibraryImage extends ImageEntity
{

	use HasCache;
	use HasImageResizer;

	private LibraryImageModel $libraryImageModel;

	public function injectService(
		LibraryImageModel $libraryImageModel,
	): void
	{
		$this->libraryImageModel = $libraryImageModel;
	}

	protected function getterUrl(): string
	{
		return IMAGES_DIR.$this->filename;
	}

	protected function getterExt(): string
	{
		return pathinfo($this->filename, PATHINFO_EXTENSION);
	}

	protected function getterFileNameWithoutExtension(): string
	{
		return pathinfo($this->filename)['filename'];
	}

	protected function getterIsSvg(): bool
	{
		if (!isset($this->cache['isSvg'])) {
			$fileInfo = pathinfo($this->filename);
			$this->cache['isSvg'] = (isset($fileInfo['extension']) && $fileInfo['extension'] === 'svg');
		}

		return $this->cache['isSvg'];
	}


	public function getSize(string $sizeName): ImageObject
	{
		if($this->width === 0 || $this->height === 0){
			[$width, $height] = $this->libraryImageModel->recalculateSizes($this);
			return $this->imageObjectFactory->getByName($this->filename, $sizeName, $this->getTimestamp(), $width, $height);
		}

		return $this->imageObjectFactory->getByName($this->filename, $sizeName, $this->getTimestamp(), $this->width, $this->height);
	}

	public function getAlt(Mutation $mutation): string
	{
		return $this->alts->{$mutation->id} ?? $this->name;
	}


	public function setAlt(Mutation $mutation, string $alt): void
	{
		$data = $this->alts;
		$data->{$mutation->id} = $alt;
		$this->alts = $data;
	}


	public function getTimestamp(): ?int
	{
		if ($this->timeOfChange !== null) {
			return $this->timeOfChange->getTimestamp();
		}

		return null;
	}
}
