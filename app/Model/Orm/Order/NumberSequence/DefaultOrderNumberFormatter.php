<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\NumberSequence;

use App\Model\Orm\Mutation\Mutation;
use DateTimeInterface;
use function sprintf;

final readonly class DefaultOrderNumberFormatter implements OrderNumberFormatter
{
	public function format(
		int $number,
		Mutation $mutation,
		DateTimeInterface $date,
	): string
	{
		return sprintf(
			'%s-%s%04d',
			$mutation->id,
			$date->format('Y'),
			$number,
		);
	}
}
