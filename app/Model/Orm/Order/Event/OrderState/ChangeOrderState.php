<?php

declare(strict_types=1);

namespace App\Model\Orm\Order\Event\OrderState;


use App\Model\Orm\Order\Order;
use App\Model\Orm\Order\OrderState;
use Symfony\Contracts\EventDispatcher\Event;


class ChangeOrderState extends Event
{

	public function __construct(
		private readonly Order $order,
		private readonly OrderState $previousOrderState
	)
	{
	}

	public function getOrder(): Order
	{
		return $this->order;
	}

	public function getPreviousOrderState(): OrderState
	{
		return $this->previousOrderState;
	}
}
