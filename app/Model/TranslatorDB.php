<?php declare(strict_types = 1);

namespace App\Model;

use App\AdminModule\Presenters\String\StringPresenter;
use App\Model\Mutation\MutationHolder;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use Nette;
use Nette\Caching\Cache;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Throwable;

final class TranslatorDB implements Nette\Localization\Translator
{

	public const ALREADY_TRANSLATED_MARKER = 'transleted_';

	private Mutation $mutation;

	private Cache $cache;

	protected array $list;

	private bool $initDone = false;

	private bool $isMarkUsage = false;

	private bool $isInsertNew = false;

	private array $usageCache = [];

	public function __construct(
		bool $insertNew,
		bool $markUsage,
		private readonly MutationHolder $mutationHolder,
		private readonly MutationsHolder $mutationsHolder,
		private readonly Orm $orm,
		Nette\Caching\Storage $cacheStorage,
	)
	{
		$this->cache = new Cache($cacheStorage, 'translations');

		$this->isInsertNew = $insertNew;
		$this->isMarkUsage = $markUsage;

	}

	public function setMutation(Mutation $mutation): void
	{
		$this->mutation = $mutation;
	}


	private function init(): void
	{
		$this->setMutation($this->mutationHolder->getMutation());
		$this->setList($this->mutation);

		$this->initDone = true;
	}


	private function setList(Mutation $mutation): void
	{
		$cacheKey = $this->getCacheKey($mutation);

		$this->list = $this->cache->load($cacheKey, function () use (&$dependencies, $mutation) {
			$dependencies[Cache::Expire] = '1 hours';
			return $this->orm->string->findBy(['lg' => $mutation->langCode])->fetchPairs('name', 'value');
		});
	}

	/**
	 * Translates the given string.
	 *
	 * @param mixed $message
	 * @param mixed ...$parameters
	 * @return string
	 */
	public function translate($message, ...$parameters): string
	{
		if (!$this->initDone) {
			$this->init();
		}

		// pokud zjistim ze msg je jiz prelozena neprekladam
		$pattern = '/^' . self::ALREADY_TRANSLATED_MARKER . '/';
		if (preg_match($pattern, $message)) {
			return preg_replace($pattern, '', $message);
		}

		$translate = '##' . $message;
		$sanitizeKey = self::getKey($message);

		if (isset($this->list[$sanitizeKey])) {
			$translate = $this->list[$sanitizeKey];
		} elseif ($this->isInsertNew) {

			foreach ($this->orm->mutation->findBy(['public' => 1]) as $mutation) {
				try {
					if (Strings::length($sanitizeKey) > 0) {
						$this->orm->string->save(null, ['name' => $sanitizeKey, 'value' => '##' . $message, 'lg' => $mutation->langCode]);
						$this->orm->flush();
					}
				} catch (Throwable) {
					// duplicita - pokousi se vlozit i pro jazyk ktery jiz existuje - preskakujeme
					bd('dupl - ' . $mutation->langCode);
				}
			}
			// reinit translate list
			$this->reInit($this->mutation);
		}

		if ($this->isMarkUsage) {
			$this->markUsage($sanitizeKey, $this->mutation);
		}

		return preg_replace('/^##/', '', $translate);
	}


	public static function getKey(string $message): string
	{
		$sanitizeKey = Strings::webalize(substr($message, 0, 250), '_', false);
		// return snake_case
		return strtolower(preg_replace('/([a-z])([A-Z])/', '$1_$2', $sanitizeKey));
	}


	public static function addTranslatedToken(string $string): string
	{
		return self::ALREADY_TRANSLATED_MARKER . $string;
	}

	public function reInit(Mutation $mutation, bool $cleanCache = true): void
	{
		$this->setMutation($mutation);
		$this->setList($mutation);

		if ($cleanCache) {
			$this->cleanCache($mutation);
		}

		$this->initDone = true;
	}

	public function cleanCache(?Mutation $mutation = null): void
	{
		if (!isset($mutation)) {
			foreach ($this->mutationsHolder->findAll(false) as $m) {
				$this->cache->remove($this->getCacheKey($m));
			}
		} else {
			$this->cache->remove($this->getCacheKey($mutation));
		}

		$this->cache->remove(StringPresenter::STRING_DATASOURCE_CACHE);
	}

	private function getCacheKey(Mutation $mutation): string
	{
		return 'l' . $mutation->langCode;
	}

	public function getMutation(): Mutation
	{
		if (!$this->initDone) {
			$this->init();
		}

		return $this->mutation;
	}

	public function markUsage(string $key, Mutation $mutation): void
	{
		if (isset($this->usageCache[$key][$mutation->id])) {
			return;
		}

		if (($entity = $this->orm->string->getBy(['name' => $key, 'lg' => $mutation->langCode])) !== null) {
			$this->usageCache[$key][$mutation->id] = 1;
			if (!isset($entity->usedAt) || $entity->usedAt < new DateTimeImmutable('-1day')) {
				$entity->usedAt = new DateTimeImmutable();
				$this->orm->string->persistAndFlush($entity);
			}
		}
	}

}
