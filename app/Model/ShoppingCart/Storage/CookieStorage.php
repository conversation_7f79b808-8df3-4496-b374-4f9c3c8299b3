<?php declare(strict_types = 1);

namespace App\Model\ShoppingCart\Storage;


use Nette\Http\Request;
use Nette\Http\Response;


final class CookieStorage implements Storage
{

	private const COOKIE_NAME = 'shoppingCartId';
	private const COOKIE_EXPIRE = '+3 month';


	public function __construct(
		private readonly Request $httpRequest,
		private readonly Response $httpResponse,
	)
	{
	}

	public function get(): ?int
	{
		$orderId = $this->httpRequest->getCookie(self::COOKIE_NAME);
		if ($orderId === null) {
			return null;
		}
		return (int) $orderId;
	}

	public function set(int $orderId): void
	{
		$this->httpResponse->setCookie(self::COOKIE_NAME, (string) $orderId, self::COOKIE_EXPIRE);
	}

	public function remove(): void
	{
		$this->httpResponse->deleteCookie(self::COOKIE_NAME);
	}
}
