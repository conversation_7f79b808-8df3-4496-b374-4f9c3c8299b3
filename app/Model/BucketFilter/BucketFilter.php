<?php declare(strict_types = 1);

namespace App\Model\BucketFilter;

use App\Model\BucketFilter\SetupCreator\BasicElasticItemListGenerator;
use App\Model\BucketFilter\SetupCreator\BoxListGenerator;
use App\Model\BucketFilter\SetupCreator\ElasticItemListGenerator;
use App\Model\ElasticSearch\IndexModel;
use App\Model\ElasticSearch\Product\ResultReader;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Orm;
use App\Model\Orm\Product\Product;
use Elastica\Query;
use Elastica\QueryBuilder;
use Elastica\ResultSet;
use Nextras\Orm\Collection\ICollection;
use stdClass;

final class BucketFilter
{

	private ?EsIndex $esIndex;

	public function __construct(
		private readonly BasicElasticItemListGenerator $basicElasticItemListGenerator,
		private readonly ElasticItemListGenerator $elasticItemListGenerator,
		private readonly BoxListGenerator $boxListGenerator,
		Mutation $mutation,
		private readonly QueryFilter $queryFilter,
		private readonly QueryBaseFilter $queryBaseFilter,
		private readonly QueryAggregation $queryAggregation,
		private readonly FilterResultMapper $filterResultMapper,
		private readonly ResultReader $productResultReader,
		private readonly IndexModel $indexModel,
		Orm $orm,
		private readonly int|float|null $minScore = null,
	)
	{
		$this->esIndex = $orm->esIndex->getProductLastActive($mutation);
	}


	public function getFilter(array $selectedParameters = []): stdClass
	{
		$query = $this->getAggregationQueryWithoutFiltering();
		$emptyCountRes = $this->tryRunQuery($query);

		$countRes = null;
		if ($selectedParameters !== []) {
			$query = $this->getAggregationQuery();
			$countRes = $this->tryRunQuery($query);
		}

		return $this->filterResultMapper->convert(
			$this->boxListGenerator->getBoxList(),
			$selectedParameters,
			$this->elasticItemListGenerator->getElasticItemList(),
			$emptyCountRes,
			$countRes
		);
	}


	public function getItems(
		int $limit,
		int $offset,
		Sort $sort,
	): Result
	{
		$query = new Query();
		$query->setFrom($offset);
		$query->setSize($limit);
		if ($this->minScore !== null) {
			$query->setMinScore($this->minScore);
		}

		foreach ($sort->getSentences() as $key => $direction) {
			$query->addSort([
				$key => $direction,
			]);
		}

		$b = new QueryBuilder();
		$boolQuery = $b->query()->bool();
		$boolQuery->addMust($this->queryBaseFilter->get($this->basicElasticItemListGenerator->getBasicElasticItemList()));
		$boolQuery->addMust($this->queryFilter->newGet($this->elasticItemListGenerator->getElasticItemList()));

		$query->setQuery($boolQuery);

		$elasticResult = $this->tryRunQuery($query);

		if ( $elasticResult === null) {
			return Result::empty();
		}

		/** @var ICollection&iterable<Product> $items */
		$items = $this->productResultReader->mapResultToEntityCollection(
			$elasticResult
		);

		return Result::from(
			$items,
			$elasticResult->count(),
			$elasticResult->getTotalHits(),
		);
	}


	private function getAggregationQueryWithoutFiltering(): Query
	{
		return $this->getAggregationQuery(false);
	}


	private function getAggregationQuery(bool $withAggregationFilters = true): Query
	{
		$query = $this->getBaseAggregationQuery();
		$aggregations = $this->queryAggregation->get($this->elasticItemListGenerator->getElasticItemList(), $withAggregationFilters);

		foreach ($aggregations as $aggregation) {
			$query->addAggregation($aggregation);
		}

		return $query;
	}


	private function tryRunQuery(Query $query): ?ResultSet
	{
		$esResult = null;
		if ($this->esIndex) {
			$esResult = $this->indexModel->getIndex($this->esIndex)->search($query);
		}

		return $esResult;
	}


	private function getBaseAggregationQuery(): Query
	{
		$query = new Query();
		$query->setSize(0);
		if ($this->minScore !== null) {
			$query->setMinScore($this->minScore);
		}
		$query->setQuery($this->queryBaseFilter->get($this->basicElasticItemListGenerator->getBasicElasticItemList()));
		return $query;
	}

}
