<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Catalog;

use App\Model\BucketFilter\Box\CheckBoxes;
use App\Model\BucketFilter\Box\Slider;
use App\Model\BucketFilter\CatalogParameter;
use App\Model\BucketFilter\ElasticItem\DiscreteValues;
use App\Model\BucketFilter\SetupCreator\BoxListGenerator;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValueFilterHelper;
use App\PostType\Page\Model\Orm\Tree;
use Closure;
use stdClass;

class BoxList implements BoxListGenerator
{

	private ?array $list;

	public function __construct(
		private readonly Tree $parameterObject,
		private readonly CatalogParameter $catalogParameter,
		private readonly ParameterValueFilterHelper $parameterValueFilterHelper,
	)
	{}


	public function getBoxList(): array
	{
		if (!isset($this->list)) {
			$this->list = $this->getBoxesForCatalog();
		}

		return $this->list;
	}


	private function getBoxesForCatalog(): array
	{
		$cfSetup = $this->catalogParameter->getParametersCfForFilter($this->parameterObject);

		$visibleCounts = [];
		$numericAsSliderIds = [];
		if ($cfSetup && isset($cfSetup->visibleParameters)) {
			foreach ($cfSetup->visibleParameters as $visibleParameter) {
				if (isset($visibleParameter->parameter) && $visibleParameter->parameter->getEntity()) {
					if (isset($visibleParameter->numberAsRange) && $visibleParameter->numberAsRange) {
						$numericAsSliderIds[] = $visibleParameter->parameter->id;
					}

					if (isset($visibleParameter->visibleCount)) {
						$visibleCounts[$visibleParameter->parameter->id] = (int) $visibleParameter->visibleCount;
					} else {
						$visibleCounts[$visibleParameter->parameter->id] = null;
					}
				}
			}
		}

		$boxes = [];
		$boxes['isInStore'] = new CheckBoxes(
			DiscreteValues::NAMESPACE_FLAGS,
			function () {
				$ret = [];
				$retItem = new stdClass(); // only items in store
				$retItem->id = 1;
				$retItem->name = 'value_is_in_store';
				$ret[] = $retItem;

				return $ret;
			},
			'filter_title_store',
			'',
			'',
			1000,
			true,
		);

		$pages = $this->parameterObject->mutation->pages;


		$boxes['isNew'] = new CheckBoxes(
			DiscreteValues::NAMESPACE_FLAGS,
			function () {
				$ret = [];
				$retItem = new stdClass(); // only items in store
				$retItem->id = 1;
				$retItem->name = 'value_is_new';
				$ret[] = $retItem;

				return $ret;
			},
			'filter_title_new',
			'',
			'',
			1000,
			true,
		);

		$boxes['price'] = new Slider(
			'filter_title_price',
			'',
			20,
			true
		);

		$esParameters = $this->catalogParameter->getPossibleParametersForCatalog($this->parameterObject);
		foreach ($esParameters as $esParameter) {
			if ($esParameter->type === Parameter::TYPE_MULTISELECT) {
				$boxes[$esParameter->uid] = new CheckBoxes(
					DiscreteValues::NAMESPACE_DIALS,
					$this->parameterValueFilterHelper->getFunctionForValues($esParameter),
					'filter_title_' . $esParameter->uid,
					$esParameter->unit,
					$esParameter->description,
					$visibleCounts[$esParameter->id],
					false,
					true,
				);
			} elseif ($esParameter->type === Parameter::TYPE_SELECT) {
				$boxes[$esParameter->uid] = new CheckBoxes(
					DiscreteValues::NAMESPACE_DIALS,
					$this->parameterValueFilterHelper->getFunctionForValues($esParameter),
					'filter_title_' . $esParameter->uid,
					$esParameter->unit,
					$esParameter->description,
					$visibleCounts[$esParameter->id],
				);
			} elseif ($esParameter->type === Parameter::TYPE_NUMBER) {
				if (in_array($esParameter->id, $numericAsSliderIds)) {
					// add range for numbers
					$boxes[$esParameter->uid] = new Slider(
						'filter_title_' . $esParameter->uid,
						$esParameter->unit,
					);
				} else {
					$boxes[$esParameter->uid] = new CheckBoxes(
						DiscreteValues::NAMESPACE_DIALS,
						$this->parameterValueFilterHelper->getFunctionForValues($esParameter),
						'filter_title_' . $esParameter->uid,
						$esParameter->unit,
						$esParameter->description,
						$visibleCounts[$esParameter->id],
					);
				}
			}
		}

		return $boxes;
	}


}
