<?php declare(strict_types = 1);

namespace App\PostType\Author\Model\Orm;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\JsonContainer;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\Blog\Model\Orm\Blog;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTag;
use App\PostType\Core\Model\ParentEntity;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $internalName {default ''}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @property AuthorLocalization[]|OneHasMany $localizations {1:M AuthorLocalization::$author}
 * @property Blog[]|ManyHasMany $blogs {m:m Blog::$authors}
 * @property BlogTag[]|ManyHasMany $tags {m:m BlogTag::$authors, isMain=true}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 */
class Author extends BaseEntity implements ParentEntity
{

	use HasCustomFields;

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): AuthorLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof AuthorLocalization);
		return $localization;
	}

}
