<form n:name="form" class="main__main" data-controller="pagemenu">
	<div class="main__header">
		{include './parts/header.latte', entityLocalization => $entityLocalization}
	</div>

	<div class="main__content scroll">

		{include './parts/content/content.latte', form => $form}

		{varType App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition $formDefinition}
		{foreach $formDefinition->extenders as $extender}
			{foreach $extender->getTemplateParts([
				App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart::TYPE_MAIN,
				App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\RelationTemplatePart::TYPE_RELATION
			]) as $templatePart}
				{include $templatePart->getTemplatePath(), form => $form, ...$templatePart->getParameters()}
			{/foreach}
		{/foreach}

		{include './parts/content/validity.latte', form => $form}
		{if $entityLocalization instanceof App\Model\Orm\Routable}
			{include './parts/content/seo.latte', form => $form}
		{/if}

		{include './parts/content/coreCustomItems.latte', form => $form}

	</div>

	<div class="main__content-side scroll">
		{include './parts/side/btns.latte'}
		{include './parts/side/state.latte', form => $form}
		{control language}

		{varType App\PostType\Core\AdminModule\Components\Form\Definition\FormDefinition $formDefinition}
		{foreach $formDefinition->extenders as $extender}
			{foreach $extender->getTemplateParts([App\PostType\Core\AdminModule\Components\Form\Definition\Extenders\Templates\CommonTemplatePart::TYPE_SIDE]) as $templatePart}
				{include $templatePart->getTemplatePath(), form => $form, ...$templatePart->getParameters()}
			{/foreach}
		{/foreach}

{*		{include './parts/side/template.latte', form=>$form}*}

		{include './parts/side/edits.latte'}
	</div>

	{capture $newItemTemplate}
		{include './parts/newItemTemplate.latte', form=>$form}
	{/capture}

</form>

{$newItemTemplate}

{include $templates . '/part/core/libraryOverlay.latte'}
