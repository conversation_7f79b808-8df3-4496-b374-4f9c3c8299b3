<?php declare(strict_types = 1);

namespace App\PostType\Page\AdminModule\Components\Form;

use App\Model\Orm\User\User;
use App\Model\StringHelper;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeModel;
use Nette\Application\UI\Form;
use Nette\Utils\ArrayHash;

final class Handler
{

	public function __construct(
		private readonly TreeModel $treeModel,
	)
	{
	}


	public function handle(User $userEntity, Tree $tree, Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();
		if (isset($values->firstTemplate)) {
			assert(property_exists($values, 'template'));
			$values->template = $values->firstTemplate;
			$valuesAll['template'] = $values->firstTemplate;
			unset($values->firstTemplate);
			unset($valuesAll->firstTemplate);
		}

		// $valuesAll['content'] = StringHelper::removeTinyMceEmptyP($valuesAll['content']);

		$tree = $this->treeModel->save($tree, $valuesAll, $values, $userEntity->id);

		$tree->flushCache();
	}

}
