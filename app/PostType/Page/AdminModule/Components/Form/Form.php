<?php declare(strict_types = 1);

namespace App\PostType\Page\AdminModule\Components\Form;

use App\AdminModule\Components\PostType\Localizations\Localizations;
use App\AdminModule\Components\PostType\Localizations\LocalizationsFactory;
use App\Model\Cloner\PageCommonCloner;
use App\Model\CustomField\SuggestUrls;
use App\Model\Link\LinkFactory;
use App\Model\Mutation\MutationsHolder;
use App\Model\Orm\Orm;
use App\Model\Orm\User\User;
use App\Model\Translator;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeModel;
use Closure;
use LogicException;
use Nette\Application\UI\Control;
use Nette\Utils\ArrayHash;

class Form extends Control
{

	public function __construct(
		private readonly Tree $tree,
		private readonly User $userEntity,
		private readonly \App\Model\Security\User $user,
		private readonly Builder $formBuilder,
		private readonly Translator $translator,
		private readonly Handler $handler,
		private readonly Orm $orm,
		private readonly MutationsHolder $mutationsHolder,
		private readonly LinkFactory $linkFactory,
		private readonly SuggestUrls $urls,
		private readonly TreeModel $treeModel,
		private readonly LocalizationsFactory $languageFactory,
	)
	{
		$this->onAnchor[] = Closure::fromCallable([$this, 'init']);
	}


	private function init(): void
	{
	}


	public function render(): void
	{
		$template = $this->template;

		$defaultMutation = $this->mutationsHolder->getDefault();

		$linksToFront = null;
		if ($this->tree->uid !== 'userLogout') {
			$linksToFront = $this->linkFactory->linkTranslateToNette($this->tree, ['show' => 1, 'mutation' => $this->tree->mutation]);
		}

		$template->add('linksToFront', $linksToFront);
		$template->add('mutations', $this->mutationsHolder->findAll(false));
		$template->add('parent', $this->tree->getParent()); // TODO fix?
		$template->add('entityLocalization', $this->tree);
		$template->add('orm', $this->orm);
		$template->add('corePartsDirectory', \App\PostType\Core\AdminModule\Components\Form\Form::TEMPLATE_PARTS_DIR);
		$template->add('templates', RS_TEMPLATE_DIR);
		$template->add('defaultMutation', $defaultMutation);
		$template->add('onMainMutation', ($this->tree->mutation === $defaultMutation));
		$template->add('fileUploadLink', $this->presenter->link('upload!', ['id' => $this->tree->id]));
		$template->add('mutation', $this->tree->mutation);
		$template->add('tree', $this->tree);
		$template->add('urls', $this->urls);
		$template->add('translator', $this->translator);
		$template->setTranslator($this->translator);
		$template->render(__DIR__ . '/form.latte');
	}


	protected function createComponentForm(): \Nette\Application\UI\Form
	{
		$from = new \Nette\Application\UI\Form();
		$postData = [];

		$this->formBuilder->build($this->user, $from, $this->tree, $postData);

		$from->setTranslator($this->translator);
		$from->onSuccess[] = [$this, 'formSucceeded'];
		$from->onError[] = [$this, 'formError'];
		$from->onValidate[] = [$this, 'formValidate'];

		return $from;
	}


	public function formValidate(\Nette\Application\UI\Form $form): void
	{
		$valuesAll = $form->getHttpData();
		$id = (int) $valuesAll['id'];
		$tree = $this->orm->tree->getById($id);

		if ($tree && $tree->editedTime && $tree->editedTime->getTimestamp() !== (int) $valuesAll['lastEdited']) {
			$user = $this->orm->user->getById($tree->edited);
			$userName = $user ? $user->name : 'Unknown';
			$msg = sprintf($this->translator->translate('form_message_error_edited_mismatch'), $userName, $tree->editedTime->format('j.n.Y H:i:s'));

			//log
			file_put_contents(LOG_DIR . '/onSave_conflict_messages.txt', date('Y-m-d H:i:s') . "\t treeId:" . $tree->id . ', user:' . $userName . "\n", FILE_APPEND);

			$form->addError($msg, false);
		}

		if ($valuesAll['validity']['publicFrom'] === '' || $valuesAll['validity']['publicTo'] === '') {
			$form->addError('form_message_error_validyti_time', false);
		}
	}




	public function formError(\Nette\Application\UI\Form $form): void
	{
//		$this->presenter->flashMessage('Error', 'error');
		bd($form->getErrors());
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formSucceeded(\Nette\Application\UI\Form $form, ArrayHash $values): void
	{
		$this->handler->handle($this->userEntity, $this->tree, $form, $values);

		$this->presenter->redirect('default', ['id' => $this->tree->id]);
	}


	public function handleDelete(): void
	{
		if ($this->tree->parent === null) {
			throw new LogicException('Can\'t delete root directory');
		}

		$canDelete = true;
		if ($this->tree->uid !== '') {
			$canDelete = $this->user->isInRole('developer');
		}


		if ($canDelete) {
			$this->treeModel->remove($this->tree);
			$this->presenter->flashMessage('OK', 'ok');
			$redirectId = $this->tree->parent === null ? $this->tree->rootId : $this->tree->parent->id;

		} else {
			$redirectId = $this->tree->id;
			$this->presenter->flashMessage('Tuto stránke nelze smazat', 'error');

		}

		$this->presenter->redirect('default', ['id' => $redirectId]);
	}


	protected function createComponentLanguage(): Localizations
	{
		assert($this->tree instanceof LocalizationEntity);
		$showDuplicateButton = ($this->tree->parent !== null);
		return $this->languageFactory->create(
			localizationEntity: $this->tree,
			showDuplicateButton: $showDuplicateButton,
			editAction: 'default',
		);
	}

}
