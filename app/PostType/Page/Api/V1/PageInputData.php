<?php

declare(strict_types=1);

namespace App\PostType\Page\Api\V1;

use Apitte\Core\Mapping\Request\BasicEntity;

final class PageInputData extends BasicEntity
{

	public string $template;

	public int $parentId;

	public ?int $commonId;

	public bool $public;

	public string $name;

	public string $nameAnchor;

	public string $nameTitle;

	public string|null $alias;

	public string $annotation;

	public string $content;

	public ?array $cf;

	public ?array $cc;

}
