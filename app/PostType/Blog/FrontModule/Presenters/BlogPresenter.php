<?php declare(strict_types = 1);

namespace App\PostType\Blog\FrontModule\Presenters;

use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Presenters\BasePresenter;
use App\PostType\Blog\FrontModule\Components\Attached\AttachedBlogs;
use App\PostType\Blog\FrontModule\Components\Attached\AttachedBlogsFactory;
use App\PostType\Blog\Model\Orm\Blog;
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Blog\Model\Orm\BlogLocalizationModel;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTag;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Blog\FrontModule\Components\BlogLocalizationStructuredData\BlogLocalizationStructuredData;
use App\PostType\Blog\FrontModule\Components\BlogLocalizationStructuredData\BlogLocalizationStructuredDataFactory;
use App\PostType\BlogTag\Model\BlogTagModel;
use Nextras\Orm\Collection\ICollection;

/**
 * @method Blog getObject()
 */
final class BlogPresenter extends BasePresenter
{

	use HasCustomContentRenderer;

	private BlogLocalization $blogLocalization;

	public function __construct(
		private AttachedBlogsFactory $attachedBlogsFactory,
		private BlogLocalizationModel $blogLocalizationModel,
		private BlogLocalizationStructuredDataFactory $blogLocalizationStructuredDataFactory,
		private BlogTagModel $blogTagModel,

	)
	{
		parent::__construct();
	}

	public function startup(): void
	{
		parent::startup();
	}


	public function actionDefault(CommonTree $object): void
	{
		$this->setObject($object);
	}


	public function renderDefault(CommonTree $object): void
	{
		$this->addComponent($this->visualPaginatorFactory->create(), 'pager');
		$this['pager']->object = $object;
		$this['pager']->special = true;

		$paginator = $this['pager']->getPaginator();
		$paginator->itemsPerPage = $this->configService->get('blog', 'paging');

//		if ($object->uid == 'blog') {
			// vse
			$blogLocalizations = $this->orm->blogLocalization->findBy(['mutation' => $this->mutation])->orderBy('publicFrom', ICollection::DESC);
//		} else {
//			$possibleBlogLocalizationIds = $this->orm->blogLocalizationTree->findBy(['tree->id' => $object->id])->fetchPairs(null, 'blogLocalization->id');
//			$blogLocalizations = $this->orm->blogLocalization->findBy(['id' => $possibleBlogLocalizationIds])->orderBy('publicFrom');
//		}

		$totalCount = $blogLocalizations->countStored();
		$paginator->itemCount = $totalCount;

		$this->template->tagsWithCount = $this->blogTagModel->getTagsWithCount($this->mutation, BlogTag::TYPE_BLOG);
		$this->template->totalCount = $totalCount;
		$this->template->blogLocalizations = $blogLocalizations->limitBy($paginator->itemsPerPage, $paginator->offset);

		if ($this->isAjax()) {
			if ($this['pager']->getParameter('more')) {
				$this->redrawControl('articlesInner');
				$this->redrawControl('articlesPagerBottom');
				$this->redrawControl('articleList');
			} else {
				if (!$this->getSignal()) {
					$this->redrawControl('articles');
					$this->redrawControl('tags');
				}
			}
		}
	}

	public function actionDetail(BlogLocalization $object): void
	{
		$this->setObject($object);
		$this->blogLocalization = $object;
	}


	public function renderDetail(): void
	{
		$this->blogLocalizationModel->increaseViews($this->blogLocalization);

		$this->template->blogLocalization = $this->blogLocalization;
		$this->template->tagsWithCount = $this->blogTagModel->getTagsWithCount($this->mutation, BlogTag::TYPE_BLOG);
	}

	public function beforeRender(): void
	{
		parent::beforeRender();
		$this->template->setFile(__DIR__ . '/../templates/' . $this->getAction() . '.latte');
	}





	protected function createComponentAttachedBlogs(): AttachedBlogs
	{
		return $this->attachedBlogsFactory->create($this->blogLocalization->blog);
	}

	protected function createComponentBlogLocalizationStructuredData(): BlogLocalizationStructuredData
	{
		return $this->blogLocalizationStructuredDataFactory->create($this->blogLocalization, $this->mutation);
	}

}
