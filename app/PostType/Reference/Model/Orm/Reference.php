<?php declare(strict_types = 1);

namespace App\PostType\Reference\Model\Orm;

use App\Model\Orm\BaseEntity;
use App\Model\Orm\JsonContainer;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Traits\HasCustomFields;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTag;
use App\PostType\ReferenceTag\Model\Orm\ReferenceTag\ReferenceTag;
use App\PostType\Core\Model\ParentEntity;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Exception\NoResultException;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;

/**
 * @property int $id {primary}
 * @property string $internalName {default ''}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 *
 * RELATIONS
 * @property ReferenceLocalization[]|OneHasMany $localizations {1:M ReferenceLocalization::$reference}
 * @property Reference[]|ManyHasMany $attachedReferences {m:m Reference::$parentReferences, orderBy=[internalName=ASC]}
 * @property Reference[]|ManyHasMany $parentReferences {m:m Reference::$attachedReferences, isMain=true, orderBy=[internalName=ASC]}
// * @property ReferenceTag[]|ManyHasMany $tags {m:m ReferenceTag::$references, isMain=true}
 * @property BlogTag[]|ManyHasMany $tags {m:m BlogTag::$references, isMain=true}
 *
 * VIRTUAL
 * @property ArrayHash|null $cf {virtual}
 */
class Reference extends BaseEntity implements ParentEntity
{

	use HasCustomFields;

	public function getInternalName(): string
	{
		return $this->internalName;
	}

	public function setInternalName(string $internalName): void
	{
		$this->internalName = $internalName;
	}

	public function getLocalizations(): ICollection
	{
		return $this->localizations->toCollection();
	}

	/**
	 * @throws NoResultException
	 */
	public function getLocalization(Mutation $mutation): ReferenceLocalization
	{
		$localization = $this->getLocalizations()->getByChecked(['mutation' => $mutation]);
		assert($localization instanceof ReferenceLocalization);
		return $localization;
	}

}
