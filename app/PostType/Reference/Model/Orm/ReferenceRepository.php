<?php declare(strict_types = 1);

namespace App\PostType\Reference\Model\Orm;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Reference getById($id)
 * @method Reference[]|ICollection searchByName(string $q, array $excluded = [])
 */
final class ReferenceRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [Reference::class];
	}

}
