<?php declare(strict_types = 1);

namespace App\PostType\Reference\FrontModule\Components\Attached;

use App\Model\TranslatorDB;
use App\PostType\Reference\Model\Orm\Reference;
use Nette\Application\UI\Control;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;

class AttachedReferences extends Control
{

	public function __construct(
		private Reference $object,
		private TranslatorDB $translator
	)
	{
	}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);

		$this->template->object = $this->object;
		$this->template->references = $this->getReferences();
		$this->template->templates = FE_TEMPLATE_DIR;

		$this->template->render(__DIR__ . '/attachedReferences.latte');
	}


	protected function getReferences(): ICollection
	{
//		TODO FIX @vojta

//		$withoutReferenceIds = [$this->object->id];
//
//		$references = $this->object->attachedReferences->toCollection()->findBy([])->limitBy($this->limit)->fetchPairs('id');
//		$withoutReferenceIds = array_merge($withoutReferenceIds, array_keys($references));
//
//
//		if (count($references) < $this->limit) { // ostatni clanky tagu
//			foreach ($this->object->referenceTagsPublic as $tag) {
//				$references += $tag->referencesPublic->findBy(['id!=' => $withoutReferenceIds])->fetchPairs('id');
//			}
//		}
//
//		if (count($references) < $this->limit) { // ostatni clanky kategorii
//			foreach ($this->object->categoriesPublic as $category) {
//				$references += $category->referencesPublic->findBy(['id!=' => $withoutReferenceIds])->fetchPairs('id');
//			}
//		}
//
//		$references = array_slice($references, 0, $this->limit, true);
//		return new ArrayIterator($references);
		return new EmptyCollection();
	}

}
