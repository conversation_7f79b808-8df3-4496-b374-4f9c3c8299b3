<?php declare(strict_types = 1);

namespace App\Api\V1\Controllers\Mutation;

use Apitte\Core\Mapping\Response\BasicEntity;
use App\Api\Entity\Response\CustomFields;
use App\Model\Orm\Mutation\Mutation;

class MutationItem extends BasicEntity
{

	public readonly int $id;

	public readonly ?string $name;

	public readonly string $langCode;

	public readonly ?string $adminEmail;

	public readonly ?string $contactEmail;

	public readonly ?string $orderEmail;

	public readonly ?string $fromEmail;

	public readonly ?string $fromEmailName;

	public readonly ?CustomFields $cf;

	public function __construct(Mutation $mutation)
	{
		$this->id = $mutation->id;
		$this->name = $mutation->name;
		$this->langCode = $mutation->langCode;
		$this->adminEmail = $mutation->adminEmail;
		$this->contactEmail = $mutation->contactEmail;
		$this->orderEmail = $mutation->orderEmail;
		$this->fromEmail = $mutation->fromEmail;
		$this->fromEmailName = $mutation->fromEmailName;
		$this->cf = CustomFields::from($mutation->cf);
	}

}
