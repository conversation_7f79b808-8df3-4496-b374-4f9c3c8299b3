<?php declare(strict_types = 1);

namespace App\Api\V1\Controllers\Mutation\List;

use Apitte\Core\Mapping\Response\BasicEntity;
use App\Api\V1\Controllers\Mutation\MutationItem;
use App\Model\Orm\Mutation\Mutation;

class MutationListResponse extends BasicEntity
{

	/** @var MutationItem[] */
	public readonly array $mutations;

	public function __construct(
		array $mutations
	)
	{
		$mutationListItems = [];
		foreach ($mutations as $item) {
			assert($item instanceof Mutation);
			$mutationListItems[] = new MutationItem($item);
		}

		$this->mutations = $mutationListItems;
	}

}
