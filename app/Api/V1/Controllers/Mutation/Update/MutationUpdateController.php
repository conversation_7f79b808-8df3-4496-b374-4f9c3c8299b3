<?php

declare(strict_types=1);

namespace App\Api\V1\Controllers\Mutation\Update;

use Apitte\Core\Annotation\Controller\Method;
use Apitte\Core\Annotation\Controller\Path;
use Apitte\Core\Annotation\Controller\RequestBody;
use Apitte\Core\Annotation\Controller\RequestParameter;
use Apitte\Core\Annotation\Controller\Response;
use Apitte\Core\Annotation\Controller\Tag;
use Apitte\Core\Http\ApiRequest;
use Apitte\Core\Http\ApiResponse;
use Apitte\Core\Schema\EndpointParameter;
use App\Api\V1\Controllers\BaseV1Controller;
use App\Api\V1\Controllers\Mutation\MutationInputData;
use App\Api\V1\Controllers\Mutation\MutationItem;
use App\Model\Orm\Orm;

#[Path('/')]
#[Tag('Mutation')]
final class MutationUpdateController extends BaseV1Controller
{
	public function __construct(
		private readonly Orm $orm,
	) {}

	#[Path('/mutation/{id}')]
	#[Method('PUT')]
	#[RequestParameter(name: 'id', type: 'int', in: EndpointParameter::IN_PATH, required: true, description: 'Mutation Id')]
	#[RequestBody(entity: MutationInputData::class, required: true)]
	#[Response(description: 'Success', code: '200', entity: MutationItem::class)]
	public function put(ApiRequest $request, ApiResponse $response): ApiResponse
	{
		$id = $request->getParameter('id');
		$mutation = $this->orm->mutation->getById($id);

		/** @var MutationInputData $inputData */
		$inputData = $request->getEntity();

		$mutation->name = $inputData->name;
		$mutation->langCode = $inputData->langCode;
		$mutation->adminEmail = $inputData->adminEmail;
		$mutation->contactEmail = $inputData->contactEmail;
		$mutation->orderEmail = $inputData->orderEmail;
		$mutation->fromEmail = $inputData->fromEmail;
		$mutation->fromEmailName = $inputData->fromEmailName;
		$mutation->cf = $inputData->cf;

		$this->orm->mutation->persistAndFlush($mutation);

		return $this->jsonResponse(
			(new MutationItem($mutation))->toResponse(),
			$response,
		);
	}
}
