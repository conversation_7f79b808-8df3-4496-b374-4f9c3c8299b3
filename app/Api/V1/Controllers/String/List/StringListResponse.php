<?php

declare(strict_types=1);

namespace App\Api\V1\Controllers\String\List;

use Apitte\Core\Mapping\Response\BasicEntity;
use App\Api\V1\Controllers\String\StringItem;

class StringListResponse extends BasicEntity
{
	/** @var StringItem[] */
	public readonly array $strings;

	/**
	 * @phpstan-param array<string, array<string, string>> $strings
	 */
	public function __construct(array $strings)
	{
		$items = [];
		foreach ($strings as $name => $translations) {
			$items[] = new StringItem((string) $name, $translations);
		}

		$this->strings = $items;
	}
}
