parameters:
	stageName: 'production'

	config:
		isDev: false
		env: production

		elastica:
			host: localhost
			port: 9200

		mutations:
			cs:
				domain: sedlakovalegal-prod.www6.superkoderi.cz
				urlPrefix: false
				rootId: 1
				hidePageId: 43
				systemPageId: 398
				internalName: česky
				mutationId: 1
				googleAnalyticsCode: 'UA-222222-1'
				robots: "index, follow"
				sitemap: true

			en:
				domain: sedlakovalegal-prod.www6.superkoderi.cz
				urlPrefix: en
				mutationId: 2
				rootId: 2
				hidePageId: 506
				internalName: anglicky
				systemPageId: 543
				googleAnalyticsCode: 'UA-222222-1'
				robots: "index, follow"
				sitemap: true


		domainUrl: "https://superadmin.www6.superkoderi.cz/"  # https://halla.www4.superkoderi.cz TODO zmenit jak se spusti ostra domena
		domainUrlPdf: "https://superadmin.www6.superkoderi.cz/"  # https://halla.www4.superkoderi.cz TODO zmenit jak se spusti ostra domena

		translations:
			insertNew: false
			markUsage: false                                  #kdyz neni zalozeny prekad v DB -> vytovri se novy

		google:
			referrer: "https://sedlakovalegal-prod.www6.superkoderi.cz/"

includes:
    - header.php

services:
	cacheStorage:
		class: Nette\Caching\Storages\FileStorage('%tempDir%/cache')

	nette.latteFactory:
		setup:
#			- setTempDirectory("../temp/cache/latte")



