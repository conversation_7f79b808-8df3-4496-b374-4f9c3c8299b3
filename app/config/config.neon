#
# SECURITY WARNING: it is CRITIC<PERSON> that this file & directory are NOT accessible directly via a web browser!
#
# If you don't protect this directory from direct web access, anybody will be able to see your passwords.
# http://nette.org/security-warning
#
parameters:
	stageName: 'dev'
	postTypeRoutes: []

	google:
		oauth:
			isPublic: false
			clientId:
			clientSecret:

	config:
		projectName: 'sedlakova'
		defaultLang: 'cs'

		imageFromStage: null
		envName: %stageName%
		consoleMode: %consoleMode%
		adminAlias: superadmin # pri zmene aliasu je potreba upravit htaccess RewriteCond %{REQUEST_URI} !/superadmin/.* ; pripadne pridat presmerovani RewriteRule ^superadmin/(.*)$ new/$1 [R=301,QSA,L]

		adminTitle: Superadmin
		adminLang: cz # cz, en
		debuggerEditor: 'phpstorm://open?file=%file&line=%line'

		translations:
			insertNew: false
			markUsage: false

		wwwDir: %wwwDir%
		mailChimp:
			api: ""
			listId: ""

		google:
			apiKey: 'AIzaSyAiwUleiQ0L9WVJZZc3Z4f7O3RRf17sCsM'
			referrer: null  # Set to your domain if API key has referrer restrictions


		userGroup: # nastavení práv pro už. skupiny

			admin: {
				setPermission: TRUE,
				sources: {}
				denySources: {
					Admin:Developer: []
					Admin:Elastic: []
					Admin:Cache: []
					Admin:ApiToken: []
				}
			}
			user: { #registered
				setPermission: TRUE,
				sources: {Admin:Sign: []}
				denySources: {}
			}

		userRoles:
			user: Registrovaný
			admin: Admin
			developer: Developer

		templatesProduct: {
			:Front:Product:detail: Product
		}


		voucherKinds:
			normal: voucher_kind_normal
			product: voucher_kind_product

		voucherTypes:
			amount: voucher_amount
			percent: voucher_percent
			amountCombination: voucher_amountCombination

		voucherApplications:
			product: voucher_app_product
			service: voucher_app_service
			all: voucher_app_all

		makeWebhook:
			url: 'https://hook.eu2.make.com/kq2avc228pvhytcrp752lcxroo3u9f2i'

php:
	date.timezone: Europe/Prague
#	zlib.output_compression: yes

mail:
	smtp: true


application:
	errorPresenter: Front:Error
	mapping:
		*: [App, *Module\Presenters, *\*Presenter]

tracy:
	maxDepth: 4

http:
	headers:
		X-Powered-By: null

session:
	expiration: 14 days
	cookie_secure: true ##TODO doesnt-work
	autoStart: false

extensions:
	logging: Contributte\Logging\DI\TracyLoggingExtension
	monolog: Contributte\Monolog\DI\MonologExtension

logging:
	logDir: %appDir%/../nettelog


includes:
	- events.neon
	- templates.neon
	- monolog.neon
	#- mutations.neon
	- services.neon
	- console.neon
	- admin.neon
	- images.neon
	- web.neon
	- webVersion.neon
	- shop.neon
	- migrations.neon
	- components.neon
	- orm.neon
	- customFields.neon
	- customContent.neon
	- elasticSearch.neon
	- redis.neon
	- robotsTxt.neon
	- postType.neon
	- state.neon
	- api.neon
	- basicAuth.neon
	- httpProxy.neon
	#- messenger.neon
	- messenger.new.neon
	- bucketFilter.neon
	- ../FrontModule/Components/config.neon
	- ../AdminModule/config.neon
	- ../Model/StaticPage/config.neon
	- ./Presenters/FormLogs/config.neon

