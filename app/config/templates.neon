parameters:
	config:
		templatesParentsRules: # X:[Y] ...pri ukladani pokud ma rodic sablonu X, nasvím novému prvku šablonu Y
#			Article:default:
#				- 'Article:default'
#				- 'Article:detail'
			:Feature:Front:Feature:servicesMain:
				- ':Feature:Front:Feature:servicesCategory'
			:Front:Page:career:
				- ':Front:Page:careerDetail'

		templates:
			# default templates Page
			:Front:Page:homepage: 'Homepage'
			:Front:Page:about: 'About'
			:Front:Page:abroad: 'Zahraničí'
			:Front:Page:contact: 'Contact'
			:Front:Page:default: 'Page'
			:Front:Page:faq: 'FAQ'
			:Front:Page:forWho: 'Pro koho'
			:Front:Page:career: 'Kariéra'
			:Front:Page:careerDetail: 'Kariéra - detail'

			#nove
			:Feature:Front:Feature:servicesMain: 'Služby - hlavní'
			:Feature:Front:Feature:servicesCategory: 'Služby - kategorie'
			:Author:Front:Author:default: 'O nás (tým) - výpis'
#			:Author:Front:Author:detail: 'O nás (tým) - detail'
			:Reference:Front:Reference:default: 'Reference - výpis'
			:Material:Front:Material:default: 'Materi<PERSON><PERSON>, akce - výpis'
#			:Reference:Front:Reference:detail: 'Reference (tým) - detail'
			:Blog:Front:Blog:default: 'Blog kategorie'
			:Blog:Front:Blog:detail: 'Blog - detail'

#			# :Front:Page:career: 'Career' # TODO postType -> změnit i v CF/CC šablonách
			# :Front:Page:careerDetail: 'Career - detail' # TODO postType -> změnit i v CF/CC šablonách
			# :Front:Page:materials: 'Materials - nepouzivat' # TODO postType -> změnit i v CF/CC šablonách
			# :Front:Page:materialsDetail: 'Materials - detail - nepouzivat' # TODO postType -> změnit i v CF/CC šablonách
